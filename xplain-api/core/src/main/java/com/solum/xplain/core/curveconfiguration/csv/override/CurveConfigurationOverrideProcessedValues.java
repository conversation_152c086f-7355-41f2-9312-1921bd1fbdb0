package com.solum.xplain.core.curveconfiguration.csv.override;

import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;

import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NewVersionFormV2Utils;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationMapper;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationOverrideForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm;
import com.solum.xplain.core.error.InfoItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.instrument.InstrumentType;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import lombok.Data;

@Data(staticConstructor = "newOf")
public class CurveConfigurationOverrideProcessedValues {

  private final CurveConfiguration curveConfiguration;
  private final List<CurveConfigurationInstrumentCsvOverride> instrumentOverrides;
  private final InfoItem infoItem;

  public List<LogItem> logItems() {
    return List.of(infoItem);
  }

  public CurveConfigurationUpdateForm toUpdateForm(ImportOptions importOptions) {
    var vf =
        NewVersionFormV2Utils.fromImportOptions(
            importOptions,
            curveConfiguration.getValidFrom(),
            importOptions::getFutureVersionsAction);
    CurveConfigurationUpdateForm updateForm =
        CurveConfigurationMapper.INSTANCE.toForm(curveConfiguration);
    updateForm.setOverrides(overrides());
    updateForm.setVersionForm(vf);
    return updateForm;
  }

  private List<CurveConfigurationOverrideForm> overrides() {
    var groupedOverrides = instrumentOverrides.stream().collect(groupOverridesMap());

    return groupedOverrides.entrySet().stream()
        .map(i -> toOverrideForm(i.getKey(), i.getValue()))
        .sorted(comparingInt(CurveConfigurationOverrideForm::getPriority))
        .toList();
  }

  private Collector<
          CurveConfigurationInstrumentCsvOverride,
          ?,
          Map<Integer, Map<InstrumentType, CurveConfigurationProviderOverrideForm>>>
      groupOverridesMap() {
    return groupingBy(
        CurveConfigurationInstrumentCsvOverride::getPriority,
        mapping(
            Function.identity(),
            Collectors.toMap(
                CurveConfigurationInstrumentCsvOverride::getInstrumentType,
                CurveConfigurationInstrumentCsvOverride::getProviderForm)));
  }

  private CurveConfigurationOverrideForm toOverrideForm(
      Integer priority, Map<InstrumentType, CurveConfigurationProviderOverrideForm> instruments) {
    var form = new CurveConfigurationOverrideForm();
    form.setEnabled(true);
    form.setPriority(priority);
    form.setInstruments(instruments);
    return form;
  }
}
