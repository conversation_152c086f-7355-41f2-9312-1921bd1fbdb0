package com.solum.xplain.core.curveconfiguration.event;

import com.solum.xplain.core.common.EntityEvent;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CurveConfigurationNameChanged extends EntityEvent {
  private final String newName;

  public CurveConfigurationNameChanged(String entityId, String newName) {
    super(entityId);
    this.newName = newName;
  }
}
