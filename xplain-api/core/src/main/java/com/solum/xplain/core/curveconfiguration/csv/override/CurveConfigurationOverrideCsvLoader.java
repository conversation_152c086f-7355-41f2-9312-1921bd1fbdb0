package com.solum.xplain.core.curveconfiguration.csv.override;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.readCsv;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateEmptyContent;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateHeaders;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.CONFIGURATION_NAME_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.INSTRUMENT_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.OVERRIDE_CURVES_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.OVERRIDE_FX_PAIR_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.OVERRIDE_SECTORS_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.PRIMARY_PROVIDER_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.SECONDARY_PROVIDER_FIELD;
import static com.solum.xplain.core.curveconfiguration.validation.OnshoreOffshoreIborFixingDepositOverridesValidator.validateIborFixingDepositOverride;
import static java.lang.String.format;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

import com.opengamma.strata.collect.io.CsvFile;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import io.atlassian.fugue.Pair;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.inject.Provider;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.stereotype.Component;

@Component
public class CurveConfigurationOverrideCsvLoader {
  static final String OVERRIDE_PRIORITY_FIELD = "Priority";
  public static final List<String> CONFIGURATION_OVERRIDES_CSV_HEADERS =
      List.of(
          CONFIGURATION_NAME_FIELD,
          OVERRIDE_PRIORITY_FIELD,
          INSTRUMENT_FIELD,
          PRIMARY_PROVIDER_FIELD,
          SECONDARY_PROVIDER_FIELD,
          OVERRIDE_CURVES_FIELD,
          OVERRIDE_FX_PAIR_FIELD,
          OVERRIDE_SECTORS_FIELD);
  private static final String INVALID_ORDER =
      "Invalid %s overrides order. " + "Overrides must be in turn";
  private final Provider<CurveConfigurationInstrumentCsvLoader> loaderProvider;

  public CurveConfigurationOverrideCsvLoader(
      Provider<CurveConfigurationInstrumentCsvLoader> loaderProvider) {
    this.loaderProvider = loaderProvider;
  }

  public Either<List<ErrorItem>, CsvParserResult<CurveConfigurationInstrumentCsvOverrides>> parse(
      byte[] csvContent, ParsingMode parsingMode, Set<String> validCurveConfigurationNames) {
    return parse(csvContent, parsingMode, row -> true, validCurveConfigurationNames);
  }

  public Either<List<ErrorItem>, CsvParserResult<CurveConfigurationInstrumentCsvOverrides>>
      parseForConfig(byte[] csvContent, ParsingMode parsingMode, String curveConfigurationName) {
    return parse(
        csvContent,
        parsingMode,
        row ->
            row.findValue(CONFIGURATION_NAME_FIELD)
                .filter(v -> v.equals(curveConfigurationName))
                .isPresent(),
        Set.of(curveConfigurationName));
  }

  private Either<List<ErrorItem>, CsvParserResult<CurveConfigurationInstrumentCsvOverrides>> parse(
      byte[] csvContent,
      ParsingMode parsingMode,
      Predicate<CsvRow> rowFilter,
      Set<String> validCurveConfigurationNames) {
    try {
      CsvFile csvFile = readCsv(csvContent);
      validateHeaders(csvFile, CONFIGURATION_OVERRIDES_CSV_HEADERS);
      validateEmptyContent(csvFile);

      CurveConfigurationInstrumentCsvLoader instrumentCsvLoader = loaderProvider.get();

      CsvParserResultBuilder<ConfigurationOverrideCsvForm, String> resultBuilder =
          new CsvParserResultBuilder<>(
              this::toUniqueKeyString, Function.identity(), false, parsingMode.failOnError());

      csvFile.rows().stream()
          .filter(rowFilter)
          .map(row -> parseRow(row, instrumentCsvLoader, validCurveConfigurationNames))
          .forEach(resultBuilder::addLine);

      return resultBuilder.toEither().flatMap(result -> validateResult(result, parsingMode));

    } catch (Exception e) {
      return Either.left(List.of(new ErrorItem(Error.PARSING_ERROR, e.getMessage())));
    }
  }

  private Either<List<ErrorItem>, CsvParserResult<CurveConfigurationInstrumentCsvOverrides>>
      validateResult(
          CsvParserResult<ConfigurationOverrideCsvForm> parserResult, ParsingMode parsingMode) {
    var resolvedForms = resolveForms(parserResult.getParsedLines());
    var validatedOverrides = validateOverrides(resolvedForms);
    var validEntries = IterableUtils.toList(Eithers.filterRight(validatedOverrides));
    var errors = IterableUtils.toList(Eithers.filterLeft(validatedOverrides));
    if (parsingMode.failOnError() && !errors.isEmpty()) {
      return Either.left(errors);
    }

    if (validEntries.isEmpty()) {
      return Either.left(List.of(Error.PARSING_ERROR.entity("No valid entries in file!")));
    }

    return Either.right(new CsvParserResult<>(validEntries, errors));
  }

  private String toUniqueKeyString(ConfigurationOverrideCsvForm form) {
    return String.format(
        "%s Order %s Instrument %s",
        form.getInstrumentCsvForm().getConfigurationName(),
        form.getPriority(),
        form.getInstrumentCsvForm().getInstrumentForm().getInstrumentType().getLabel());
  }

  private List<Either<ErrorItem, CurveConfigurationInstrumentCsvOverrides>> validateOverrides(
      List<CurveConfigurationInstrumentCsvOverrides> overridesList) {
    return overridesList.stream()
        .map(
            override ->
                validateOffshoreOverrides(override).flatMap(this::validateOverridePriorities))
        .toList();
  }

  private Either<ErrorItem, CurveConfigurationInstrumentCsvOverrides> validateOffshoreOverrides(
      CurveConfigurationInstrumentCsvOverrides form) {
    var overrides = form.getOverrides();
    for (CurveConfigurationInstrumentCsvOverride override : overrides) {
      if (override.getInstrumentType().equals(CoreInstrumentType.IBOR_FIXING_DEPOSIT)) {
        List<String> curveNames = override.getProviderForm().getAssetNames();
        for (String curveName : curveNames) {
          Optional<ErrorItem> errorItem = validateIborFixingDepositOverride(curveName, curveNames);
          if (errorItem.isPresent()) {
            return Either.left(errorItem.get());
          }
        }
      }
    }
    return Either.right(form);
  }

  private Either<ErrorItem, CurveConfigurationInstrumentCsvOverrides> validateOverridePriorities(
      CurveConfigurationInstrumentCsvOverrides configurationOverrides) {
    var overridePriorities =
        configurationOverrides.getOverrides().stream()
            .map(CurveConfigurationInstrumentCsvOverride::getPriority)
            .distinct()
            .sorted()
            .toList();
    for (int i = 0; i < overridePriorities.size(); i++) {
      if (overridePriorities.get(i) != i) {
        return Either.left(
            Error.PARSING_ERROR.entity(
                format(INVALID_ORDER, configurationOverrides.getConfigurationName())));
      }
    }
    return Either.right(configurationOverrides);
  }

  private Either<ErrorItem, Pair<Integer, ConfigurationOverrideCsvForm>> parseRow(
      CsvRow row,
      CurveConfigurationInstrumentCsvLoader instrumentCsvLoader,
      Set<String> validCurveConfigurationNames) {
    return Steps.begin(parseOverrideOrder(row))
        .then(() -> instrumentCsvLoader.parseOverrideRow(row, validCurveConfigurationNames))
        .yield(ConfigurationOverrideCsvForm::newOf)
        .map(form -> new Pair<>(row.lineNumber(), form));
  }

  private Either<ErrorItem, Integer> parseOverrideOrder(CsvRow row) {
    return Checked.now(() -> CsvLoaderUtils.parseNonNegativeInteger(row, OVERRIDE_PRIORITY_FIELD))
        .toEither()
        .leftMap(err -> rowParsingError(row, OVERRIDE_PRIORITY_FIELD, err));
  }

  private List<CurveConfigurationInstrumentCsvOverrides> resolveForms(
      List<ConfigurationOverrideCsvForm> forms) {
    return forms.stream()
        .collect(
            groupingBy(
                ConfigurationOverrideCsvForm::configurationName,
                mapping(this::toInstrumentForm, toList())))
        .entrySet()
        .stream()
        .map(l -> CurveConfigurationInstrumentCsvOverrides.newOf(l.getKey(), l.getValue()))
        .toList();
  }

  private CurveConfigurationInstrumentCsvOverride toInstrumentForm(
      ConfigurationOverrideCsvForm form) {
    return CurveConfigurationInstrumentCsvOverride.newOf(
        form.getPriority(), form.getInstrumentCsvForm().getInstrumentForm());
  }
}
