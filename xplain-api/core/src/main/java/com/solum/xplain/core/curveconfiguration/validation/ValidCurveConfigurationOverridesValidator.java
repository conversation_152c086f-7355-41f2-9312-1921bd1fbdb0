package com.solum.xplain.core.curveconfiguration.validation;

import static java.lang.String.format;
import static org.apache.commons.collections4.CollectionUtils.getCardinalityMap;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationOverrideForm;
import com.solum.xplain.core.instrument.InstrumentType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class ValidCurveConfigurationOverridesValidator
    implements ConstraintValidator<
        ValidCurveConfigurationOverrides, List<CurveConfigurationOverrideForm>> {

  @Override
  public boolean isValid(
      List<CurveConfigurationOverrideForm> overrideForms, ConstraintValidatorContext context) {
    if (isEmpty(overrideForms)) {
      return true;
    } else if (!areOverridesPrioritiesValid(overrideForms)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("Priorities must be unique and aligned!")
          .addConstraintViolation();
      return false;
    } else {
      var duplicatedCurves = duplicatedOverrideFields(overrideForms);
      if (!duplicatedCurves.isEmpty()) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(
                format("%s have duplicate overrides", duplicatedCurves))
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }

  private boolean areOverridesPrioritiesValid(List<CurveConfigurationOverrideForm> overrideForms) {
    var priorities =
        overrideForms.stream().map(CurveConfigurationOverrideForm::getPriority).distinct().toList();
    return priorities.size() == overrideForms.size();
  }

  private List<String> duplicatedOverrideFields(
      List<CurveConfigurationOverrideForm> overrideForms) {
    Map<String, List<InstrumentType>> curveInstrumentTypes = new HashMap<>();
    for (var override : overrideForms) {
      for (var e : override.getInstruments().entrySet()) {
        for (var c : e.getValue().overrideFields()) {
          var ii = curveInstrumentTypes.getOrDefault(c, new ArrayList<>());
          curveInstrumentTypes.putIfAbsent(c, ii);
          ii.add(e.getKey());
        }
      }
    }
    return curveInstrumentTypes.entrySet().stream()
        .filter(e -> !duplicates(e.getValue()).isEmpty())
        .map(Map.Entry::getKey)
        .toList();
  }

  private <T> List<T> duplicates(final List<T> a) {
    return getCardinalityMap(a).entrySet().stream()
        .filter(e -> e.getValue() > 1)
        .map(Map.Entry::getKey)
        .toList();
  }
}
