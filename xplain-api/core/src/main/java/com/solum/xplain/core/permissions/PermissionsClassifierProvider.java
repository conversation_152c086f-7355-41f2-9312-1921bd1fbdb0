package com.solum.xplain.core.permissions;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import com.solum.xplain.core.permissions.extension.PermissionCategory;
import com.solum.xplain.core.permissions.extension.PermissionCategoryGroup;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class PermissionsClassifierProvider implements ClassifiersProvider {
  private final List<PermissionCategory> categories;

  public PermissionsClassifierProvider(PermissionCategoryAggregator permissionCategoryAggregator) {
    this.categories = permissionCategoryAggregator.categories();
  }

  @Override
  public List<Classifier> classifiers() {
    var categoriesClassifier =
        categories.stream()
            .collect(
                Collectors.collectingAndThen(
                    Collectors.groupingBy(PermissionCategory::getGroup), this::toClassifier));
    var classifier = new Classifier("taskSubCategoriesByCategories", categoriesClassifier);
    return List.of(classifier);
  }

  @Override
  public int sortOrder() {
    return 4;
  }

  private List<Classifier> toClassifier(
      Map<PermissionCategoryGroup, List<PermissionCategory>> groupMap) {
    return groupMap.entrySet().stream()
        .map(c -> new Classifier(c.getKey().name(), toClassifiers(c.getValue())))
        .toList();
  }

  private List<Classifier> toClassifiers(List<PermissionCategory> categories) {
    return categories.stream().map(PermissionCategory::name).map(Classifier::new).toList();
  }
}
