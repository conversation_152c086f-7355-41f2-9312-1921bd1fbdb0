package com.solum.xplain.core.common.validation;

import com.solum.xplain.core.classifiers.Constants;
import com.solum.xplain.core.utils.FrequencyUtils;
import java.util.Collection;
import java.util.function.Supplier;

public class SwapLegFrequencySupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Constants.SWAP_LEG_FREQUENCIES.stream().map(FrequencyUtils::toStringNoPrefix).toList();
  }
}
