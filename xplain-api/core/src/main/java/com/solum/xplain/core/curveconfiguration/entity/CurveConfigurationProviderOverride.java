package com.solum.xplain.core.curveconfiguration.entity;

import com.solum.xplain.extensions.enums.CreditSector;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@FieldNameConstants
public class CurveConfigurationProviderOverride extends MarketDataProviders {

  private List<String> assetNames;
  private List<String> fxPairs;
  private List<CreditSector> sectors;
}
