package com.solum.xplain.core.permissions.provider;

import com.solum.xplain.core.permissions.CorePermissionCategory;
import com.solum.xplain.core.permissions.extension.PermissionCategory;
import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CorePermissionCategoryProvider implements PermissionCategoryProvider {

  @Override
  public List<PermissionCategory> permissionCategories() {
    return Arrays.asList(CorePermissionCategory.values());
  }
}
