package com.solum.xplain.core.curveconfiguration.csv.override;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(staticName = "newOf")
public class ConfigurationOverrideCsvForm {
  private final Integer priority;
  private final ConfigurationOverrideInstrumentCsvForm instrumentCsvForm;

  public String configurationName() {
    return instrumentCsvForm.getConfigurationName();
  }
}
