package com.solum.xplain.core.curveconfiguration.validation;

import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.instrument.InstrumentTypeResolver;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class ValidCurveConfigInstrumentSizeValidator
    implements ConstraintValidator<
        ValidCurveConfigInstrumentSize, Map<InstrumentType, ? extends MarketDataProviderForm>> {

  private final int expectedInstrumentTypeCount;

  public ValidCurveConfigInstrumentSizeValidator(InstrumentTypeResolver instrumentTypeResolver) {
    this.expectedInstrumentTypeCount =
        (int)
            instrumentTypeResolver.values().stream()
                .filter(InstrumentType::isCurveConfigInstrument)
                .count();
  }

  @Override
  public boolean isValid(
      Map<InstrumentType, ? extends MarketDataProviderForm> value,
      ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    var keys = value.keySet();
    return keys.size() == expectedInstrumentTypeCount;
  }
}
