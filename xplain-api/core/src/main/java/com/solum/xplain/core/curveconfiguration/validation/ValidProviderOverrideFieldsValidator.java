package com.solum.xplain.core.curveconfiguration.validation;

import static org.apache.commons.collections4.CollectionUtils.isEmpty;

import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideForm;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Map;

public class ValidProviderOverrideFieldsValidator
    implements ConstraintValidator<
        ValidProviderOverrideFields, Map<InstrumentType, CurveConfigurationProviderOverrideForm>> {

  @Override
  public boolean isValid(
      Map<InstrumentType, CurveConfigurationProviderOverrideForm> value,
      ConstraintValidatorContext context) {
    if (value != null) {
      return value.entrySet().stream().allMatch(v -> validateOverride(v.getKey(), v.getValue()));
    }
    return true;
  }

  private boolean validateOverride(
      InstrumentType type, CurveConfigurationProviderOverrideForm form) {
    if (type.getInstrumentGroup() == CoreInstrumentGroup.ALL_FX) {
      return isEmpty(form.getSectors()) && isEmpty(form.getAssetNames());
    } else if (type.getInstrumentGroup() == CoreInstrumentGroup.ALL_CREDIT) {
      return isEmpty(form.getAssetNames()) && isEmpty(form.getFxPairs());
    } else {
      return isEmpty(form.getFxPairs()) && isEmpty(form.getSectors());
    }
  }
}
