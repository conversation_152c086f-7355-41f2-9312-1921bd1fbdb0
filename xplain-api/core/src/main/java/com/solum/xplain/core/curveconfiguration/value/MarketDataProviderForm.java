package com.solum.xplain.core.curveconfiguration.value;

import com.solum.xplain.core.common.validation.ValidDataProviderCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarketDataProviderForm {

  @ValidDataProviderCode private String primary;
  @ValidDataProviderCode private String secondary;

  public static MarketDataProviderForm emptyForm() {
    return new MarketDataProviderForm(null, null);
  }
}
