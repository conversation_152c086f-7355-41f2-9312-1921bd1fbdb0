package com.solum.xplain.valuation.mapper

import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING

import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.product.swap.FixedRateCalculation
import com.opengamma.strata.product.swap.IborRateCalculation
import com.opengamma.strata.product.swap.InflationRateCalculation
import com.opengamma.strata.product.swap.OvernightRateCalculation
import com.opengamma.strata.product.swap.RateCalculationSwapLeg
import com.opengamma.strata.product.swap.SwapTrade
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import com.solum.xplain.extensions.index.OffshoreIndices
import com.solum.xplain.extensions.index.OvernightTermIndex
import com.solum.xplain.extensions.index.OvernightTermRateCalculation
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails
import com.solum.xplain.valuation.messages.trade.constant.SwapLegType
import java.time.LocalDate
import spock.lang.Specification

class SwapTradeMapperTest extends Specification {
  def tradeMapper = new SwapTradeMapper(new SwapLegMapper())

  def "should map Fixed-Ibor SwapTrade"() {
    setup:
    def trade = tradeMapper.toStrataTrade(fixedIborSwap())
    expect:
    trade != null
    trade instanceof SwapTrade
    trade.product.startDate.unadjusted == LocalDate.parse("2018-04-16")
    trade.product.endDate.unadjusted == LocalDate.parse("2019-04-16")
    trade.product.payLeg.isPresent()
    trade.product.payLeg.get().getCurrency().getCode() == "EUR"
    def payLeg = (RateCalculationSwapLeg) trade.product.payLeg.get()
    payLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    payLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    payLeg.getAccrualSchedule().getBusinessDayAdjustment().convention == MODIFIED_FOLLOWING

    payLeg.getCalculation() instanceof FixedRateCalculation
    ((FixedRateCalculation) payLeg.getCalculation()).getRate().getInitialValue() == 1

    def receiveLeg = (RateCalculationSwapLeg) trade.product.receiveLeg.get()
    receiveLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    receiveLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    receiveLeg.getAccrualSchedule().getBusinessDayAdjustment().convention == MODIFIED_FOLLOWING
    receiveLeg.getCalculation() instanceof IborRateCalculation
    ((IborRateCalculation) receiveLeg.getCalculation()).getSpread().get().getInitialValue() == 1
    ((IborRateCalculation) receiveLeg.getCalculation()).getIndex().toString() == "EUR-EURIBOR-3M"
  }

  def "should map Fixed-Ibor Offshore SwapTrade"() {
    setup:
    def trade = tradeMapper.toStrataTrade(fixedIborSwapOffshore())
    expect:
    trade != null
    trade instanceof SwapTrade
    trade.product.startDate.unadjusted == LocalDate.parse("2018-04-16")
    trade.product.endDate.unadjusted == LocalDate.parse("2019-04-16")
    trade.product.payLeg.isPresent()
    trade.product.payLeg.get().getCurrency().getCode() == "TWD"
    def payLeg = (RateCalculationSwapLeg) trade.product.payLeg.get()
    payLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    payLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    payLeg.getAccrualSchedule().getBusinessDayAdjustment().convention == MODIFIED_FOLLOWING

    payLeg.getCalculation() instanceof FixedRateCalculation
    ((FixedRateCalculation) payLeg.getCalculation()).getRate().getInitialValue() == 1

    def receiveLeg = (RateCalculationSwapLeg) trade.product.receiveLeg.get()
    receiveLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    receiveLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    receiveLeg.getAccrualSchedule().getBusinessDayAdjustment().convention == MODIFIED_FOLLOWING
    receiveLeg.getCalculation() instanceof IborRateCalculation
    ((IborRateCalculation) receiveLeg.getCalculation()).getSpread().get().getInitialValue() == 1
    ((IborRateCalculation) receiveLeg.getCalculation()).getIndex().toString() == "TWD-TAIBOR-OFFSHORE-3M"
  }

  def "should map Fixed-Overnight SwapTrade"() {
    setup:
    def trade = tradeMapper.toStrataTrade(fixedOvernightSwap())
    expect:
    trade != null
    trade instanceof SwapTrade
    trade.product.startDate.unadjusted == LocalDate.parse("2018-04-16")
    trade.product.endDate.unadjusted == LocalDate.parse("2019-04-16")
    trade.product.payLeg.isPresent()
    trade.product.payLeg.get().getCurrency().getCode() == "EUR"
    def payLeg = (RateCalculationSwapLeg) trade.product.payLeg.get()
    payLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    payLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    payLeg.getAccrualSchedule().getBusinessDayAdjustment() == BusinessDayAdjustment.NONE
    payLeg.getCalculation() instanceof FixedRateCalculation
    ((FixedRateCalculation) payLeg.getCalculation()).getRate().getInitialValue() == 1

    def receiveLeg = (RateCalculationSwapLeg) trade.product.receiveLeg.get()
    receiveLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    receiveLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    receiveLeg.getAccrualSchedule().getBusinessDayAdjustment() == BusinessDayAdjustment.NONE
    receiveLeg.getCalculation() instanceof OvernightRateCalculation
    ((OvernightRateCalculation) receiveLeg.getCalculation()).getSpread().get().getInitialValue() == 1
    ((OvernightRateCalculation) receiveLeg.getCalculation()).getIndex().toString() == "EUR-ESTR"
  }

  def "should map Fixed-Overnight Offshore SwapTrade"() {
    setup:
    def trade = tradeMapper.toStrataTrade(fixedOvernightSwapOffshore())
    expect:
    trade != null
    trade instanceof SwapTrade
    trade.product.startDate.unadjusted == LocalDate.parse("2018-04-16")
    trade.product.endDate.unadjusted == LocalDate.parse("2019-04-16")
    trade.product.payLeg.isPresent()
    trade.product.payLeg.get().getCurrency().getCode() == "THB"
    def payLeg = (RateCalculationSwapLeg) trade.product.payLeg.get()
    payLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    payLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    payLeg.getAccrualSchedule().getBusinessDayAdjustment() == BusinessDayAdjustment.NONE
    payLeg.getCalculation() instanceof FixedRateCalculation
    ((FixedRateCalculation) payLeg.getCalculation()).getRate().getInitialValue() == 1

    def receiveLeg = (RateCalculationSwapLeg) trade.product.receiveLeg.get()
    receiveLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    receiveLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    receiveLeg.getAccrualSchedule().getBusinessDayAdjustment() == BusinessDayAdjustment.NONE
    receiveLeg.getCalculation() instanceof OvernightRateCalculation
    ((OvernightRateCalculation) receiveLeg.getCalculation()).getSpread().get().getInitialValue() == 1
    ((OvernightRateCalculation) receiveLeg.getCalculation()).getIndex() == OffshoreIndices.THB_THOR_OFFSHORE
  }

  def "should map Fixed-Inflation SwapTrade"() {
    setup:
    def trade = tradeMapper.toStrataTrade(fixedInflation())
    expect:
    trade != null
    trade instanceof SwapTrade
    trade.product.startDate.unadjusted == LocalDate.parse("2018-04-16")
    trade.product.endDate.unadjusted == LocalDate.parse("2019-04-16")
    trade.product.payLeg.isPresent()
    trade.product.payLeg.get().getCurrency().getCode() == "EUR"
    def payLeg = (RateCalculationSwapLeg) trade.product.payLeg.get()
    payLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    payLeg.getCalculation() instanceof FixedRateCalculation
    ((FixedRateCalculation) payLeg.getCalculation()).getRate().getInitialValue() == 1

    def receiveLeg = (RateCalculationSwapLeg) trade.product.receiveLeg.get()
    receiveLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    receiveLeg.getCalculation() instanceof InflationRateCalculation
    ((InflationRateCalculation) receiveLeg.getCalculation()).getIndex().toString() == "GB-RPI"
  }

  def "should map Fixed-TermOvernight SwapTrade"() {
    setup:
    def trade = tradeMapper.toStrataTrade(fixedTermOvernightSwap())
    expect:
    trade != null
    trade instanceof SwapTrade
    trade.product.startDate.unadjusted == LocalDate.parse("2025-05-12")
    trade.product.endDate.unadjusted == LocalDate.parse("2026-05-12")
    trade.product.payLeg.isPresent()
    trade.product.payLeg.get().getCurrency().getCode() == "USD"

    def payLeg = (RateCalculationSwapLeg) trade.product.payLeg.get()
    payLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    payLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    payLeg.getCalculation() instanceof FixedRateCalculation
    ((FixedRateCalculation) payLeg.getCalculation()).getRate().getInitialValue() == 1

    def receiveLeg = (RateCalculationSwapLeg) trade.product.receiveLeg.get()
    receiveLeg.getPaymentSchedule().getPaymentDateOffset().getAdjustment().getConvention() == FOLLOWING
    receiveLeg.getPaymentSchedule().getBusinessDayAdjustment().get().convention == MODIFIED_FOLLOWING
    receiveLeg.getCalculation() instanceof OvernightTermRateCalculation
    ((OvernightTermRateCalculation) receiveLeg.getCalculation()).getIndex().toString() == OvernightTermIndex.of("USD-SOFR-6M").toString()
    ((OvernightTermRateCalculation) receiveLeg.getCalculation()).getSpread().get().getInitialValue() == 1
  }

  static ValuationRequest fixedTermOvernightSwap() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "USGS",
      startDate: LocalDate.parse("2025-05-12"),
      endDate: LocalDate.parse("2026-05-12"),
      payLeg: fixedLeg(true, "USD"),
      receiveLeg: termOvernightLeg(false),
      businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "USD"),
      )
      )
  }



  static ValuationRequest fixedIborSwap() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "EUTA",
      startDate: LocalDate.parse("2018-04-16"),
      endDate: LocalDate.parse("2019-04-16"),
      payLeg: fixedLeg(true),
      receiveLeg: iborLeg(false),
      businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "EUR"),
      )
      )
  }

  static ValuationRequest fixedIborSwapOffshore() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "EUTA",
      startDate: LocalDate.parse("2018-04-16"),
      endDate: LocalDate.parse("2019-04-16"),
      payLeg: fixedLeg(true).tap { it.currency = "TWD" },
      receiveLeg: iborLeg(false).tap({
        it.currency = "TWD"
        it.index = "TWD-TAIBOR-3M"
        it.isOffshore = true
      }),
      businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "TWD"),
      )
      )
  }

  static ValuationRequest fixedOvernightSwap() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "EUTA",
      startDate: LocalDate.parse("2018-04-16"),
      endDate: LocalDate.parse("2019-04-16"),
      payLeg: fixedLeg(true),
      receiveLeg: overnightLeg(false),
      businessDayAdjustmentType: BusinessDayAdjustmentType.PAYMENT_ONLY,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "EUR"),
      )
      )
  }

  static ValuationRequest fixedOvernightSwapOffshore() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "EUTA",
      startDate: LocalDate.parse("2018-04-16"),
      endDate: LocalDate.parse("2019-04-16"),
      payLeg: fixedLeg(true).tap {
        it.currency = "THB"
      },
      receiveLeg: overnightLeg(false).tap {
        it.currency = "THB"
        it.index = "THB-THOR"
        it.isOffshore = true
      },
      businessDayAdjustmentType: BusinessDayAdjustmentType.PAYMENT_ONLY,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "EUR"),
      )
      )
  }

  static ValuationRequest fixedInflation() {
    new ValuationRequest(
      externalTradeId: "ext",
      tradeDetails: new ValuationTradeDetails(
      calendar: "EUTA",
      startDate: LocalDate.parse("2018-04-16"),
      endDate: LocalDate.parse("2019-04-16"),
      payLeg: fixedLeg(true),
      receiveLeg: inflationLeg(false),
      businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT,
      businessDayConvention: "ModifiedFollowing",
      notionalScheduleInitialExchange: false,
      notionalScheduleFinalExchange: false,
      stubConvention: "LONG_FINAL",
      info: new ValuationTradeInfo(tradeCurrency: "EUR"),
      )
      )
  }

  static ValuationTradeLegDetails iborLeg(boolean isPay) {
    new ValuationTradeLegDetails(
      type: SwapLegType.IBOR,
      notional: 1,
      currency: "EUR",
      accrualFrequency: "1M",
      paymentOffsetDays: -1,
      paymentFrequency: "1M",
      paymentCompounding: "None",
      initialValue: 1,
      fixingDateOffsetDays: 2,
      index: "EUR-EURIBOR-3M",
      dayCount: "Act/360"
      )
  }

  static ValuationTradeLegDetails fixedLeg(boolean isPay, String currency = "EUR") {
    new ValuationTradeLegDetails(
      type: SwapLegType.FIXED,
      accrualFrequency: "1M",
      paymentOffsetDays: -1,
      paymentFrequency: "1M",
      paymentCompounding: "None",
      notional: 1,
      currency: currency,
      initialValue: 1,
      dayCount: "Act/360"
      )
  }

  static ValuationTradeLegDetails termOvernightLeg(boolean isPay) {
    new ValuationTradeLegDetails(
      type: SwapLegType.TERM_OVERNIGHT,
      notional: 1,
      currency: "USD",
      accrualFrequency: "6M",
      initialValue: 1,
      paymentFrequency: "6M",
      fixingDateOffsetDays: -2,
      index: "USD-SOFR-6M",
      accrualMethod: "COMPOUNDED",
      overnightRateCutOffDays: 0,
      dayCount: "Act/360"
      )
  }


  static ValuationTradeLegDetails overnightLeg(boolean isPay) {
    new ValuationTradeLegDetails(
      type: SwapLegType.OVERNIGHT,
      notional: 1,
      currency: "EUR",
      accrualFrequency: "1M",
      paymentOffsetDays: -1,
      paymentFrequency: "1M",
      paymentCompounding: "None",
      initialValue: 1,
      fixingDateOffsetDays: 2,
      index: "EUR-ESTR",
      accrualMethod: "COMPOUNDED",
      overnightRateCutOffDays: 1,
      dayCount: "Act/360"
      )
  }

  static ValuationTradeLegDetails inflationLeg(boolean isPay) {
    new ValuationTradeLegDetails(
      type: SwapLegType.INFLATION,
      notional: 1,
      currency: "EUR",
      accrualFrequency: "1M",
      paymentOffsetDays: -1,
      paymentFrequency: "1M",
      paymentCompounding: "None",
      inflationLag: "2M",
      index: "GB-RPI",
      indexCalculationMethod: "INTERPOLATED",
      )
  }
}
