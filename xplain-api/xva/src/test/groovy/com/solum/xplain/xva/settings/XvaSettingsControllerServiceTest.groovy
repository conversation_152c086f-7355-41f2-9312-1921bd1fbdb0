package com.solum.xplain.xva.settings

import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.settings.value.GlobalSettingsSearch
import com.solum.xplain.xva.settings.repository.XvaLiborIndicesRepository
import com.solum.xplain.xva.settings.repository.XvaOvernightIndicesRepository
import com.solum.xplain.xva.settings.repository.XvaSettingsRepository
import com.solum.xplain.xva.settings.value.XvaLiborIndicesView
import com.solum.xplain.xva.settings.value.XvaOvernightIndicesView
import com.solum.xplain.xva.settings.value.XvaSettingsView
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class XvaSettingsControllerServiceTest extends Specification {

  private static final def STATE_DATE = BitemporalDate.newOf(LocalDate.now())

  def xvaSettingsRepository = Mock(XvaSettingsRepository)
  def xvaLiborIndicesRepository = Mock(XvaLiborIndicesRepository)
  def xvaOvernightIndicesRepository = Mock(XvaOvernightIndicesRepository)

  def service = new XvaSettingsControllerService(
  xvaSettingsRepository,
  xvaLiborIndicesRepository,
  xvaOvernightIndicesRepository
  )

  def "should get settings"() {
    given:
    def expected = new XvaSettingsView()
    1 * xvaSettingsRepository.getXvaSettings(STATE_DATE) >> expected

    expect:
    service.getXvaSettings(STATE_DATE) == expected
  }

  @Unroll
  def "should get #indexType indices"() {
    given:
    def expectedView = indexType == 'libor' ? new XvaLiborIndicesView() : new XvaOvernightIndicesView()
    if (indexType == 'libor') {
      1 * xvaLiborIndicesRepository.entityView(STATE_DATE) >> expectedView
    } else {
      1 * xvaOvernightIndicesRepository.entityView(STATE_DATE) >> expectedView
    }

    expect:
    def result = indexType == 'libor' ?
      service.getLiborIndices(STATE_DATE) :
      service.getOvernightIndices(STATE_DATE)

    result == expectedView

    where:
    indexType << ['libor', 'overnight']
  }

  @Unroll
  def "should get #indexType indices versions"() {
    given:
    if (indexType == 'libor') {
      1 * xvaLiborIndicesRepository.entityVersions() >> []
    } else {
      1 * xvaOvernightIndicesRepository.entityVersions() >> []
    }

    expect:
    def result = indexType == 'libor' ?
      service.xvaLiborIndicesVersions() :
      service.xvaOvernightIndicesVersions()

    result == []

    where:
    indexType << ['libor', 'overnight']
  }

  @Unroll
  def "should get #indexType indices future versions"() {
    given:
    def actualDate = STATE_DATE.getActualDate()
    def expected = new DateList([])
    if (indexType == 'libor') {
      1 * xvaLiborIndicesRepository.futureVersions(actualDate) >> expected
    } else {
      1 * xvaOvernightIndicesRepository.futureVersions(actualDate) >> expected
    }

    expect:
    def search = new GlobalSettingsSearch(actualDate)
    def result = indexType == 'libor' ?
      service.xvaLiborIndicesFutureVersions(search) :
      service.xvaOvernightIndicesFutureVersions(search)

    result == expected

    where:
    indexType << ['libor', 'overnight']
  }
}
