package com.solum.xplain.core.calibrationapi;

import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import java.util.List;
import java.util.stream.Stream;

public interface CurveCalibrationResultsProvider {
  Stream<CurveCalibrationResult> resultEntities(
      List<String> curveId,
      CalibratedCurvesOptions calibrationOptions,
      CurveConfigMarketStateForm stateForm);

  CurveCalibrationResult latestResult(String curveId);

  Stream<CurveCalibrationResult> latestResults(List<String> curveId);

  void clearCalibrationResults(List<String> curveIds);
}
