package com.solum.xplain.core.authentication.converter;

import com.google.common.annotations.VisibleForTesting;
import com.solum.xplain.core.authentication.CacheableAuthenticationProxy;
import com.solum.xplain.core.authentication.XplainPrincipalResolver;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.config.properties.OAuth2CustomProperties;
import java.util.ArrayList;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.context.annotation.Profile;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;
import org.springframework.security.oauth2.server.resource.introspection.SpringOpaqueTokenIntrospector;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

@Component
@ConditionalOnProperty(name = "spring.security.oauth2.resourceserver.opaquetoken.introspection-uri")
@Profile("!mockMvc")
public class OpaqueTokenToPrincipalIntrospector implements OpaqueTokenIntrospector {
  private final SpringOpaqueTokenIntrospector springOpaqueTokenIntrospector;
  private final XplainPrincipalResolver xplainPrincipalResolver;
  private final CacheableAuthenticationProxy cacheableAuthenticationProxy;
  private final OAuth2CustomProperties oAuth2CustomProperties;

  @Autowired
  public OpaqueTokenToPrincipalIntrospector(
      OAuth2ResourceServerProperties properties,
      XplainPrincipalResolver xplainPrincipalResolver,
      CacheableAuthenticationProxy cacheableAuthenticationProxy,
      OAuth2CustomProperties oAuth2CustomProperties) {
    this(
        new SpringOpaqueTokenIntrospector(
            properties.getOpaquetoken().getIntrospectionUri(),
            properties.getOpaquetoken().getClientId(),
            properties.getOpaquetoken().getClientSecret()),
        xplainPrincipalResolver,
        cacheableAuthenticationProxy,
        oAuth2CustomProperties);
  }

  @VisibleForTesting
  OpaqueTokenToPrincipalIntrospector(
      SpringOpaqueTokenIntrospector springOpaqueTokenIntrospector,
      XplainPrincipalResolver xplainPrincipalResolver,
      CacheableAuthenticationProxy cacheableAuthenticationProxy,
      OAuth2CustomProperties oAuth2CustomProperties) {
    this.springOpaqueTokenIntrospector = springOpaqueTokenIntrospector;
    this.xplainPrincipalResolver = xplainPrincipalResolver;
    this.cacheableAuthenticationProxy = cacheableAuthenticationProxy;
    this.oAuth2CustomProperties = oAuth2CustomProperties;
    Assert.notNull(
        oAuth2CustomProperties.getClaims(), "OAuth2 Opaque token attributes not configured");
  }

  @Override
  public OAuth2AuthenticatedPrincipal introspect(String token) {
    return cacheableAuthenticationProxy.fetchCachedOrNewPrincipal(token, this::resolvePrincipal);
  }

  @VisibleForTesting
  XplainPrincipal resolvePrincipal(String token) {
    var principal = springOpaqueTokenIntrospector.introspect(token);
    return xplainPrincipalResolver.resolvePrincipal(
        principal.getName(),
        principal.getAttribute(oAuth2CustomProperties.getClaims().getUsernameClaim()),
        principal.getAttribute(oAuth2CustomProperties.getClaims().getClientIdClaim()),
        principal.getAttribute(oAuth2CustomProperties.getClaims().getRolesClaim()),
        principal.getAttribute(oAuth2CustomProperties.getClaims().getTeamsClaim()),
        new ArrayList<>(principal.getAuthorities()),
        principal.getAttributes(),
        Objects.equals(
            principal.getAttribute(oAuth2CustomProperties.getClaims().getPrincipalTypeClaim()),
            oAuth2CustomProperties.getClaims().getPrincipalTypeClientValue()));
  }
}
