package com.solum.xplain.core.customfield;

import com.solum.xplain.core.common.form.FormMapper;
import com.solum.xplain.core.customfield.value.CustomFieldNameCreateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameUpdateForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public interface CustomFieldNameMapper
    extends FormMapper<CustomFieldNameCreateForm, CustomFieldNameUpdateForm, CustomFieldName> {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", ignore = true)
  CustomFieldName toEntity(CustomFieldNameCreateForm form);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "externalId", ignore = true)
  CustomFieldName toEntity(CustomFieldNameUpdateForm form, @MappingTarget CustomFieldName entity);

  CustomFieldName copy(CustomFieldName entity);
}
