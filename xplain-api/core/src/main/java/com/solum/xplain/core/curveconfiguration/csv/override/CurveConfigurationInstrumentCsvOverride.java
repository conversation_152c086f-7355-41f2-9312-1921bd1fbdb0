package com.solum.xplain.core.curveconfiguration.csv.override;

import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationProviderOverride;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideForm;
import com.solum.xplain.core.instrument.InstrumentType;
import java.util.Collection;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class CurveConfigurationInstrumentCsvOverride {

  private final Integer priority;
  private final InstrumentType instrumentType;
  private final CurveConfigurationProviderOverrideForm providerForm;

  public static CurveConfigurationInstrumentCsvOverride newOf(
      Integer order, ConfigurationOverrideInstrumentProviderCsvForm instrumentForm) {
    return new CurveConfigurationInstrumentCsvOverride(
        order, instrumentForm.getInstrumentType(), instrumentForm.getProvider());
  }

  public static CurveConfigurationInstrumentCsvOverride newOf(
      Integer order,
      InstrumentType instrumentType,
      CurveConfigurationProviderOverride providerForm) {
    var sectors =
        Optional.ofNullable(providerForm.getSectors()).stream()
            .flatMap(Collection::stream)
            .map(Enum::name)
            .toList();
    var form =
        CurveConfigurationProviderOverrideForm.newOf(
            providerForm.getPrimary(),
            providerForm.getSecondary(),
            providerForm.getAssetNames(),
            sectors,
            providerForm.getFxPairs());
    return new CurveConfigurationInstrumentCsvOverride(order, instrumentType, form);
  }

  public CurveConfigurationInstrumentOverrideKey uniqueKey() {
    return new CurveConfigurationInstrumentOverrideKey(priority, instrumentType);
  }
}
