package com.solum.xplain.core.curveconfiguration;

import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvemarket.MarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketData;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValue;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMapperFunction;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketData;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class CurveConfigurationMarketValueResolver {
  private final CurveGroupRepository curveGroupRepository;
  private final CurveConfigurationRepository curveConfigurationRepository;
  private final MarketDataGroupRepository marketDataGroupRepository;

  public Either<ErrorItem, CalculationMarketData> curveConfigurationMarketData(
      MarketDataExtractionParams mdParams, Map<String, ResolvedMarketData> resolvedValues) {
    return curveConfigurationRepository
        .curveConfigInstrResolver(mdParams.getStateDate(), mdParams.getConfigurationId())
        .map(cc -> resolveCurveGroupMarketData(cc, mdParams, resolvedValues));
  }

  private CalculationMarketData resolveCurveGroupMarketData(
      CurveConfigurationInstrumentResolver resolver,
      MarketDataExtractionParams mdParams,
      Map<String, ResolvedMarketData> resolvedValues) {
    List<InstrumentDefinition> definitions =
        curveGroupRepository.allInstruments(resolver.getCurveGroupId(), mdParams.getStateDate());
    return CalculationMarketData.builder()
        .curveConfigurationId(mdParams.getConfigurationId())
        .marketDataGroupId(mdParams.getMarketDataGroupId())
        .marketDataSourceType(mdParams.getMarketDataSource())
        .name(
            marketDataGroupRepository
                .dataGroupName(mdParams.getMarketDataGroupId())
                .map(EntityNameView::getName)
                .orElse(null))
        .values(
            resolveMarketDataValue(
                resolvedValues,
                resolver,
                mdParams.getPriceTypeResolver(),
                definitions,
                mdParams.getMarketDataSource().getToView()))
        .build();
  }

  public <T extends CalculationMarketValue> List<T> resolveMarketData(
      BitemporalDate stateDate,
      String curveConfigurationId,
      InstrumentPriceTypeResolver priceTypeResolver,
      Map<String, ResolvedMarketData> values,
      CalculationMarketValueMapperFunction<T> toValue) {
    return curveConfigurationRepository
        .curveConfigInstrResolver(stateDate, curveConfigurationId)
        .map(
            cc ->
                resolveMarketDataValue(
                    values,
                    cc,
                    priceTypeResolver,
                    curveGroupRepository.allInstruments(cc.getCurveGroupId(), stateDate),
                    toValue))
        .toOptional()
        .orElse(List.of());
  }

  private <T extends CalculationMarketValue> List<T> resolveMarketDataValue(
      Map<String, ResolvedMarketData> values,
      CurveConfigurationInstrumentResolver resolver,
      InstrumentPriceTypeResolver priceTypeResolver,
      List<InstrumentDefinition> definitions,
      CalculationMarketValueMapperFunction<T> toValue) {
    return Stream.ofNullable(definitions)
        .flatMap(Collection::stream)
        .flatMap(
            definition ->
                resolver.resolveProvider(definition).stream()
                    .flatMap(
                        v ->
                            resolveProviderValue(
                                definition.getKey(),
                                priceTypeResolver.resolvePriceType(definition),
                                v,
                                values,
                                toValue)
                                .stream()))
        .toList();
  }

  private <T extends CalculationMarketValue> Optional<T> resolveProviderValue(
      String key,
      InstrumentPriceType priceType,
      MarketDataProviders provider,
      Map<String, ResolvedMarketData> values,
      CalculationMarketValueMapperFunction<T> toValue) {
    return Optional.ofNullable(values.get(key))
        .map(ResolvedMarketData::getValues)
        .flatMap(v -> toValue.toValue(key, provider, v, priceType));
  }
}
