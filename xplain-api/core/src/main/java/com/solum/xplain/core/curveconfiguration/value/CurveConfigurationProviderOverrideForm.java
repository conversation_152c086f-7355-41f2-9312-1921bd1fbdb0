package com.solum.xplain.core.curveconfiguration.value;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SECTOR_CLASSIFIER_NAME;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.FxPairsSupplier;
import com.solum.xplain.core.common.validation.ValidCollectionStringSet;
import com.solum.xplain.core.curveconfiguration.validation.AssetNameSupplier;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CurveConfigurationProviderOverrideForm extends MarketDataProviderForm {

  @ValidCollectionStringSet(AssetNameSupplier.class)
  private List<String> assetNames;

  @ValidCollectionStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = SECTOR_CLASSIFIER_NAME)
  private List<String> sectors;

  @ValidCollectionStringSet(value = FxPairsSupplier.class)
  private List<String> fxPairs;

  public static CurveConfigurationProviderOverrideForm newOf(
      String primary,
      String secondary,
      List<String> assetNames,
      List<String> sectors,
      List<String> fxPairs) {
    var form = new CurveConfigurationProviderOverrideForm();
    form.setPrimary(primary);
    form.setSecondary(secondary);
    form.setAssetNames(assetNames);
    form.setSectors(sectors);
    form.setFxPairs(fxPairs);
    return form;
  }

  public List<String> overrideFields() {
    return ImmutableList.<String>builder()
        .addAll(emptyIfNull(assetNames))
        .addAll(emptyIfNull(sectors))
        .addAll(emptyIfNull(fxPairs))
        .build();
  }
}
