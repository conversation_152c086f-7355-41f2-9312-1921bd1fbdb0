package com.solum.xplain.core.curveconfiguration.csv.instrument;

import com.solum.xplain.core.instrument.InstrumentType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ConfigurationInstrumentCsvForm {

  private final String configurationName;
  private final ConfigurationInstrumentProviderCsvForm instrumentForm;

  public static ConfigurationInstrumentCsvForm newOf(
      String configurationName,
      InstrumentType type,
      String primaryProvider,
      String secondaryProvider) {
    var instrumentForm =
        ConfigurationInstrumentProviderCsvForm.newOf(type, primaryProvider, secondaryProvider);
    return new ConfigurationInstrumentCsvForm(configurationName, instrumentForm);
  }
}
