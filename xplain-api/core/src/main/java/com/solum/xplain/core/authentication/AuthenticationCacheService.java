package com.solum.xplain.core.authentication;

import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.authentication.value.CachedPrincipal;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.config.properties.OAuth2CustomProperties;
import com.solum.xplain.shared.datagrid.DataGrid;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AuthenticationCacheService {

  static final String AUTH_CACHE_KEY = "AUTH";
  private final DataGrid dataGrid;
  private final int cacheDuration;

  public AuthenticationCacheService(
      DataGrid dataGrid, OAuth2CustomProperties oAuth2CustomProperties) {
    this.dataGrid = dataGrid;
    this.cacheDuration = oAuth2CustomProperties.getTokenCacheDurationMinutes();
  }

  public void cachePrincipal(String hashedToken, CachedPrincipal cachedPrincipal) {
    dataGrid
        .getKeyValueCache(AUTH_CACHE_KEY)
        .set(hashedToken, cachedPrincipal, Duration.ofMinutes(cacheDuration));
  }

  public Optional<CachedPrincipal> fetchCachedPrincipal(String hashedToken) {
    try {
      return ofNullable(
          dataGrid.<String, CachedPrincipal>getKeyValueCache(AUTH_CACHE_KEY).get(hashedToken));
    } catch (RuntimeException e) {
      log.warn(
          "Cached principal for token {} cannot be deserialized (probably due to a version change)",
          hashedToken,
          e);
      return empty();
    }
  }

  public List<CachedPrincipal> fetchAllCachedPrincipals() {
    try {
      return List.copyOf(
          dataGrid.<String, CachedPrincipal>getKeyValueCache(AUTH_CACHE_KEY).values());
    } catch (RuntimeException e) {
      log.warn(
          "Cached principals cannot be deserialized (probably due to a version change), returning empty list",
          e);
      return Collections.emptyList();
    }
  }

  public void revokeCachedPrincipal(XplainPrincipal principal) {
    dataGrid.<String, CachedPrincipal>getKeyValueCache(AUTH_CACHE_KEY).entrySet().stream()
        .filter(e -> e.getValue().getPrincipal().getId().equals(principal.getId()))
        .forEach(e -> cachePrincipal(e.getKey(), e.getValue().revoke()));
  }
}
