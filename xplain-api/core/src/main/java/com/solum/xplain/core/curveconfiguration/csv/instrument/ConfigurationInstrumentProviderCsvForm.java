package com.solum.xplain.core.curveconfiguration.csv.instrument;

import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm;
import com.solum.xplain.core.instrument.InstrumentType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ConfigurationInstrumentProviderCsvForm {

  private final InstrumentType instrumentType;
  private final MarketDataProviderForm provider;

  public static ConfigurationInstrumentProviderCsvForm newOf(
      InstrumentType type, String primaryProvider, String secondaryProvider) {
    var provider = new MarketDataProviderForm(primaryProvider, secondaryProvider);
    return new ConfigurationInstrumentProviderCsvForm(type, provider);
  }
}
