package com.solum.xplain.core.calculationapi;

import com.solum.xplain.core.portfolio.value.OnboardingValuationMetrics;
import com.solum.xplain.core.portfolio.value.PortfolioItemCalculatedExcMngmntView;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;

public interface CalculationResultProvider {

  Optional<ObjectId> latestDashboardCalculation(
      String portfolioId, LocalDate valuationDate, String marketDataGroupId);

  Map<String, ObjectId> latestDashboardCalculationsByPortfolioId(String dashboardId);

  List<PortfolioItemCalculatedExcMngmntView> calculationResultExceptionMngmntItems(
      ObjectId calculationId);

  List<OnboardingValuationMetrics> calculationResultsOnboarding(ObjectId calculationId);
}
