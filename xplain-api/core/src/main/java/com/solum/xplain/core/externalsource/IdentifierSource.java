package com.solum.xplain.core.externalsource;

import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.Archivable;
import com.solum.xplain.core.common.diff.Diffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = IdentifierSource.IDENTIFIER_SOURCE_COLLECTION)
@Data
@EqualsAndHashCode(exclude = {"createdAt", "lastModifiedAt"})
@FieldNameConstants
public class IdentifierSource implements Archivable<IdentifierSource>, Diffable<IdentifierSource> {
  public static final String IDENTIFIER_SOURCE_COLLECTION = "identifierSource";

  @Id private String id;
  private String name;
  private String externalSourceId;
  private boolean archived;
  @CreatedBy private AuditUser createdBy;
  @LastModifiedBy private AuditUser lastModifiedBy;
  @CreatedDate private LocalDateTime createdAt;
  @LastModifiedDate private LocalDateTime lastModifiedAt;
  private List<AuditLog> auditLogs;

  @Override
  public void addAuditLog(AuditLog log) {
    if (auditLogs == null) {
      auditLogs = new ArrayList<>();
    }
    auditLogs.add(log);
  }

  @Override
  public IdentifierSource archived() {
    this.archived = true;
    return this;
  }

  @Override
  public VersionDiffs diff(IdentifierSource obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append(
                IdentifierSource.Fields.externalSourceId,
                this.externalSourceId,
                obj.externalSourceId)
            .append(IdentifierSource.Fields.name, this.name, obj.name)
            .build());
  }
}
