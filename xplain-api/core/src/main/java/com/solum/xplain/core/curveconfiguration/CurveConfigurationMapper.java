package com.solum.xplain.core.curveconfiguration;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationOverride;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationNameView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationOverrideForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationOverrideView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {ObjectIdMapper.class, AuditUserMapper.class})
public interface CurveConfigurationMapper extends VersionedEntityMapper<CurveConfiguration> {

  CurveConfigurationMapper INSTANCE = Mappers.getMapper(CurveConfigurationMapper.class);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  CurveConfiguration fromForm(CurveConfigurationForm form, @MappingTarget CurveConfiguration c);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  CurveConfiguration fromForm(
      CurveConfigurationUpdateForm form, @MappingTarget CurveConfiguration c);

  CurveConfigurationOverride fromForm(CurveConfigurationOverrideForm form);

  MarketDataProviders fromForm(MarketDataProviderForm form);

  CurveConfigurationOverrideView from(CurveConfigurationOverride override);

  CurveConfigurationProviderView from(MarketDataProviders provider);

  CurveConfigurationView from(CurveConfiguration c, String curveGroupName);

  @Mapping(target = "id", source = "entityId")
  CurveConfigurationNameView from(CurveConfiguration c);

  @Mapping(target = "versionForm", ignore = true)
  CurveConfigurationUpdateForm toForm(CurveConfiguration curveConfiguration);
}
