package com.solum.xplain.xva.helpers;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;

@WithMockUser
@ActiveProfiles("mockMvc")
@EnableSpringDataWebSupport
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({TestMapperConfig.class, HttpEncodingAutoConfiguration.class, TestMvcConfig.class})
public @interface MockMvcConfiguration {}
