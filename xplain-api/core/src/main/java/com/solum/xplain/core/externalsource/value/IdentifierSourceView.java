package com.solum.xplain.core.externalsource.value;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class IdentifierSourceView {

  @Schema(description = "Immutable unique internal id identifier for source")
  @NotEmpty
  private String id;

  @Schema(description = "Immutable unique identifier for source of external identifier")
  @NotEmpty
  private String externalSourceId;

  @Schema(description = "Name for source of external identifier")
  @NotEmpty
  private String name;
}
