package com.solum.xplain.core.curveconfiguration.entity;

import java.io.Serializable;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;

@Data
@FieldNameConstants
public class MarketDataProviders implements Serializable {

  private String primary;
  private String secondary;

  public boolean isEmpty() {
    return StringUtils.isAllEmpty(primary, secondary);
  }

  /**
   * Return the secondary provider name, or the primary provider name if there is no secondary.
   *
   * @return secondary provider name, or the primary provider name if there is no secondary.
   * @deprecated using this is bug-prone since you may unexpectedly get the primary provider, use
   *     {@link #getSecondary()} instead.
   */
  @Deprecated(since = "2.0.0")
  public Optional<String> secondaryProvider() {
    return Optional.ofNullable(secondary).or(this::primaryProvider);
  }

  public Optional<String> primaryProvider() {
    return Optional.ofNullable(primary);
  }

  public Stream<String> toProvidersStream() {
    return Stream.of(primary, secondary).filter(StringUtils::isNotEmpty);
  }
}
