package com.solum.xplain.core.curveconfiguration;

import static java.util.Objects.requireNonNull;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationOverride;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationProviderOverride;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.instrument.InstrumentType;
import io.atlassian.fugue.Pair;
import java.io.Serializable;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@FieldNameConstants
public class CurveConfigurationInstrumentResolver implements Serializable {

  private String id;
  private String name;
  private String curveGroupId;
  private Map<String, MarketDataProviders> instruments;
  private List<CurveConfigurationOverride> overrides;

  public static CurveConfigurationInstrumentResolver empty() {
    return new CurveConfigurationInstrumentResolver(null, null, null, null, null);
  }

  public static CurveConfigurationInstrumentResolver fromConfiguration(
      CurveConfiguration curveConfiguration) {
    requireNonNull(
        curveConfiguration.getInstruments(), "Curve configuration instruments must no be null");
    var instruments =
        curveConfiguration.getInstruments().entrySet().stream()
            .collect(Collectors.toMap(v -> v.getKey().name(), Entry::getValue));

    return new CurveConfigurationInstrumentResolver(
        curveConfiguration.getEntityId(),
        curveConfiguration.getName(),
        curveConfiguration.getCurveGroupId(),
        instruments,
        curveConfiguration.getOverrides());
  }

  /**
   * Returns all mapped providers from the configuration for a given instrument definition.
   *
   * <p>TODO(SXSD-9959): This method should be refactored to use as little as possible from
   * Instrument definition.
   *
   * @param curveConfigurationInstrumentResolvers the list of curve configuration instrument
   *     resolvers
   * @param instrument the instrument definition to resolve
   */
  public static Set<String> allMappedProviders(
      List<CurveConfigurationInstrumentResolver> curveConfigurationInstrumentResolvers,
      InstrumentDefinition instrument) {
    Set<String> allMappedProviders = new HashSet<>();
    for (CurveConfigurationInstrumentResolver curveConfig : curveConfigurationInstrumentResolvers) {
      Optional<MarketDataProviders> providerOpt = curveConfig.resolveProvider(instrument);
      providerOpt.ifPresent(
          provider -> {
            if (provider.getPrimary() != null) {
              allMappedProviders.add(provider.getPrimary());
            }
            if (provider.getSecondary() != null) {
              allMappedProviders.add(provider.getSecondary());
            }
          });
    }
    return allMappedProviders;
  }

  public Optional<MarketDataProviders> resolveProvider(InstrumentDefinition instrument) {
    var overrideKey = CurveConfigurationOverrideKey.key(instrument);
    return ofNullable(resolveOverrides(overrides))
        .map(ov -> ov.get(overrideKey))
        .or(() -> ofNullable(instruments).map(i -> i.get(instrument.getInstrument().name())));
  }

  private Map<CurveConfigurationOverrideKey, MarketDataProviders> resolveOverrides(
      List<CurveConfigurationOverride> overrides) {
    return Stream.ofNullable(overrides)
        .flatMap(Collection::stream)
        .filter(CurveConfigurationOverride::getEnabled)
        .sorted(Comparator.comparingInt(CurveConfigurationOverride::getPriority).reversed())
        .map(CurveConfigurationOverride::getInstruments)
        .map(Map::entrySet)
        .flatMap(Collection::stream)
        .flatMap(v -> overridePairs(v.getKey(), v.getValue()))
        .collect(Collectors.toMap(Pair::left, Pair::right, (a, b) -> a));
  }

  private Stream<Pair<CurveConfigurationOverrideKey, MarketDataProviders>> overridePairs(
      InstrumentType type, CurveConfigurationProviderOverride provider) {
    return overrideKeys(type, provider).map(v -> Pair.pair(v, provider));
  }

  private Stream<CurveConfigurationOverrideKey> overrideKeys(
      InstrumentType type, CurveConfigurationProviderOverride provider) {
    if (Objects.equals(type.getAssetClass().getGroup(), CoreAssetGroup.CREDIT)) {
      return Optional.ofNullable(provider.getSectors()).stream()
          .flatMap(Collection::stream)
          .map(c -> CurveConfigurationOverrideKey.sectorOverrideKey(type, c));
    } else if (Objects.equals(type.getAssetClass().getGroup(), CoreAssetGroup.FX)) {
      return Optional.ofNullable(provider.getFxPairs()).stream()
          .flatMap(Collection::stream)
          .map(c -> CurveConfigurationOverrideKey.currencyPairOverrideKey(type, c));
    }
    return Optional.ofNullable(provider.getAssetNames()).stream()
        .flatMap(Collection::stream)
        .map(c -> CurveConfigurationOverrideKey.assetNameOverrideKey(type, c));
  }

  public String getId() {
    return id;
  }

  public String getName() {
    return name;
  }

  public String getCurveGroupId() {
    return curveGroupId;
  }

  public Map<String, MarketDataProviders> getInstruments() {
    return instruments;
  }

  public List<CurveConfigurationOverride> getOverrides() {
    return overrides;
  }
}
