package com.solum.xplain.core.curveconfiguration;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.curveconfiguration.csv.configuration.CurveConfigurationCsvMapper.CURVE_CONFIGURATION_CSV_HEADER;
import static com.solum.xplain.core.curveconfiguration.csv.override.CurveConfigurationOverrideCsvLoader.CONFIGURATION_OVERRIDES_CSV_HEADERS;

import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.csv.configuration.CurveConfigurationCsvMapper;
import com.solum.xplain.core.curveconfiguration.csv.override.CurveConfigurationOverrideCsvMapper;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
public class CurveConfigurationExportService {

  private final CurveConfigurationRepository repository;

  public CurveConfigurationExportService(CurveConfigurationRepository repository) {
    this.repository = repository;
  }

  public Either<ErrorItem, FileResponseEntity> getOneConfigurationCsv(
      String entityId, BitemporalDate stateDate) {
    var prefix = "CurveConfigurations";
    return curveConfiguration(entityId, stateDate)
        .map(
            c ->
                csvResponseEntity(
                    nameWithTimeStamp(c.getName(), prefix, stateDate.getActualDate()),
                    CURVE_CONFIGURATION_CSV_HEADER,
                    CurveConfigurationCsvMapper.toCsvRows(c)));
  }

  public FileResponseEntity getAllConfigurationsCsv(BitemporalDate stateDate, Sort sort) {
    var csvFileName = nameWithTimeStamp("CurveConfigurations", stateDate.getActualDate());
    var curveConfigurations = repository.curveConfigurationViews(stateDate, active(), sort);
    var rows =
        curveConfigurations.stream()
            .map(CurveConfigurationCsvMapper::toCsvRows)
            .flatMap(Collection::stream)
            .toList();
    return csvResponseEntity(csvFileName, CURVE_CONFIGURATION_CSV_HEADER, rows);
  }

  public Either<ErrorItem, FileResponseEntity> getConfigurationOverridesCsv(
      String entityId, BitemporalDate stateDate) {
    var prefix = "CurveConfigurationOverride";
    return curveConfiguration(entityId, stateDate)
        .map(
            c ->
                csvResponseEntity(
                    nameWithTimeStamp(c.getName(), prefix, stateDate.getActualDate()),
                    CONFIGURATION_OVERRIDES_CSV_HEADERS,
                    CurveConfigurationOverrideCsvMapper.toCsvRows(c)));
  }

  private FileResponseEntity csvResponseEntity(
      String fileName, List<String> header, List<CsvRow> rows) {
    var csvFile = new CsvOutputFile(header, rows);
    return FileResponseEntity.csvFile(csvFile.writeToByteArray(), fileName);
  }

  private Either<ErrorItem, CurveConfigurationView> curveConfiguration(
      String entityId, BitemporalDate stateDate) {
    return repository.getView(entityId, stateDate);
  }
}
