com.solum.xplain.core.roles.value.RoleForm.externalId.pattern=External ID field cannot have whitespaces at the beginning and end.
com.solum.xplain.core.teams.value.TeamForm.externalId.pattern=External ID field cannot have whitespaces at the beginning and end.
com.solum.xplain.api.excmngmt.form.VerificationForm.comment=Comment field must not be empty
com.solum.xplain.api.excmngmt.form.VerificationForm.status=Approval Status is required
com.solum.xplain.api.excmngmt.process.form.ResolutionForm.comment=Comment field must not be empty
com.solum.xplain.api.excmngmt.process.form.ResolutionForm.value=Value field must not be empty
com.solum.xplain.api.excmngmt.process.form.ResolutionForm.resolutionType=Resolution Method is required
com.solum.xplain.api.excmngmt.processipv.form.ResolutionForm.comment=Comment field must not be empty
com.solum.xplain.api.portfolio.form.dayCount=Only \"Act/360\" value is allowed.
com.solum.xplain.api.portfolio.validation.UniquePortfolioExtId.message=Portfolio ID must be unique
com.solum.xplain.trs.portfolio.trade.validation.RequiredTrsLegCalculation.message=This field is required.
com.solum.xplain.trs.nonMtmPortfolio.validation.UniqueNonMtmPortfolioExtId.message=NON-MTM Portfolio ID must be unique
com.solum.xplain.trs.index.validation.ValidTrsIndexCurrency.message=TRS index currency is not valid
com.solum.xplain.trs.index.validation.ValidTrsIndexValidator.message=TRS index value is not valid
com.solum.xplain.trs.portfolio.trade.validation.ValidTrsLegCompoundingMethod.message=Compounding must be None
com.solum.xplain.trs.portfolio.trade.validation.PaymentOffsetDaysGreaterThanAccrualOffsetDays.message=Payment OffsetDays should be greater than Accrual Offset Days
com.solum.xplain.trs.portfolio.trade.validation.PerformanceLegRequired.message=One of the legs should be a performance leg
com.solum.xplain.trs.portfolio.trade.validation.RequiredRegularDates.message=First regular start date and last regular end date are required
com.solum.xplain.trs.portfolio.trade.validation.ValidStartDateBeforeEndDate.message=The end date occurs before the start date
com.solum.xplain.trs.portfolio.trade.validation.ValidRegularStartDateBeforeRegularEndDate.message=The regular end date occurs before the regular start date
com.solum.xplain.trs.portfolio.trade.form.tradeCounterparty=Only \"BILATERAL\" value is allowed.
com.solum.xplain.api.portfolio.validation.ValidLoanNoteReference.message=Reference is not valid for given currency
com.solum.xplain.api.ipv.group.validation.UniqueIpvDataGroupName.message=IPV data group name must be valid and unique
com.solum.xplain.api.ipv.group.validation.ImmutableIpvDataGroupName.message=IPV data group name can't be edited
com.solum.xplain.api.ipv.group.validation.ExistingIpvDataGroupName.message=IPV data group does not exist with this name
com.solum.xplain.api.ipv.data.validation.UniqueIpvDataProviderValue.message=Value already exists
com.solum.xplain.api.ipv.data.validation.NavProviderOnlyValueSupported.message=NAV must not have delta/vega values
com.solum.xplain.api.market.validation.UniqueMarketDataKey.message=Market Data Key must be unique
com.solum.xplain.core.market.validation.ValidClassifier.message=Invalid Classifier value
com.solum.xplain.api.settings.form.validation.ValidSeasonalities.message=Additive factors for Inflation seasonality should add to zero
com.solum.xplain.api.md.validation.UniqueMarketData.message=Value already exists
com.solum.xplain.api.common.validation.UpperCase.message=The value must be upper-case.
com.solum.xplain.api.teams.validation.UniqueTeamId.message=External id must be unique
com.solum.xplain.api.roles.validation.UniqueRoleId.message=External id must be unique
UniqueExternalId=External id must be unique!
ValidIdentifier.message=Value can't contain whitespaces or lowercase characters.
ValidExternalIdentifier.message=Value can't contain non-printing characters.
ValidReferenceIdentifier.message=Value must start with REF_.
ValidIpvBreakTestForm.ObjectAlreadyExists=IPV break test already exists with the same name!
ValidIpvBreakTestForm.InvalidType=Type is not supported for selected scope!
ValidIpvBreakTestForm.InvalidMeasureType=Measure is not supported for selected type!
ValidIpvBreakTestForm.InvalidTypeModification=Type can not be changed!
ValidOnboardingBreakTestForm.ObjectAlreadyExists=Onboarding break test already exists with the same name!
ValidOnboardingBreakTestForm.InvalidMeasureType=Measure is not supported for selected type!
ValidOnboardingBreakTestForm.InvalidTypeModification=Type can not be changed!
ValidStaleThreshold=Observation period must be single and be greater than 1!
ValidThresholdMustNotBeLessThanZero=Threshold can not be less than zero!
ValidThresholdMustNotContainNull=Threshold must not be null!
ValidThresholdEqMustBeSingle=Only single threshold allowed when EQ!
ValidThresholdMustBeSorted=Multiple threshold must be sorted!
ValidIdentifierSourceExtIdRestricted=External source id must not be XPLAIN!
ValidIdentifierSourceExtIdUnique=External source id must be unique!
ValidIdentifierSourceNotFound=External source does not exist!
ValidCustomFieldName=Custom Field Name does not exist!
ValidPeriod.YMW=Invalid period. Only W/M/Y allowed(2Y, 3M, 4W).
ValidPeriod.YM=Invalid period. Only M/Y allowed (2Y, 3M).
ValidPeriod.BRL=Invalid period. Only M/Q/S/Y (2M, 3Q, 4S, 5Y) and 1D allowed.
ValidPeriod.ANY=Invalid period. Only D/W/M/Y (1D, 2Y, 3M, 4W) allowed.
ValidPeriod.Y369M=Invalid period. Only 3M/6M/9M/Y allowed (6M, 2Y).
ValidPeriod.Y0369M=Invalid period. Only 0M/3M/6M/9M/Y allowed (6M, 2Y).
ValidPeriod.Y=Invalid period. Only Y allowed (2Y, 3Y).
ValidPeriod.MARKET_TENOR=Invalid period. Only D/W/M/Y/ON allowed(1D, 2Y, 3M, 4W, ON).
com.solum.xplain.api.classifiers.discountingGroup.ValidIndexBasedDiscountCurrencies.message=Invalid index based discount currencies
ValidDiscountingCurrencies.MultipleEURDiscountingCurrencies=Single EUR currency is allowed (EUR/EUR-EONIA/EUR-ESTR)
ValidDiscountingCurrencies.MultipleUSDDiscountingCurrencies=Single USD currency is allowed (USD/USD-FEDFUNDS/USD-SOFR)
InvalidGranularities.taskGranularityTypes=Unknown granularities passed
InvalidNoneGranularity.taskGranularityTypes=None granularity type can't be used with other types
InvalidCurveNameGranularity.taskGranularityTypes=Curve name granularity type can't be used with other types
# Curves
com.solum.xplain.api.curvectd.validation.UniqueCheapestToDeliverCurveNode.message=CTD nodes must be unique
com.solum.xplain.api.curvectd.validation.UniqueCTDName.message=CTD name must be unique
com.solum.xplain.api.curvegroup.curvecredit.validation.UniqueCreditCurve.message=Credit curve ID must be unique
com.solum.xplain.api.curve.validation.ValidCreditCurrencyValidator.message=Credit currency is not valid
ValidCreditCurveUpdateForm.CdxFundingNodesNotAllowed=Index curve can not have funding nodes
ValidCreditCurveUpdateForm.CdsFundingNodesNotAllowed=Selected seniority does not allow funding nodes
com.solum.xplain.api.curvegroup.curvecredit.validation.ValidFundingNodes.message=Selected seniority does not allow funding nodes
com.solum.xplain.api.curvegroup.curvecredit.validation.FundingNodesAllowed.message=Funding nodes only available with XVA
com.solum.xplain.api.curvegroup.volatility.validation.UniqueVolatilitySurfaceName.message=IR Volatility Surface name must be unique
com.solum.xplain.api.company.validation.ValidCompanyIdValidator.message=Company does not exist
com.solum.xplain.api.company.validation.UniqueProvidersValidator.message=Providers are not unique
com.solum.xplain.api.company.validation.ValidNullPriorityProvidersValidator.message=Providers cannot be non-null when a higher-priority provider is null
com.solum.xplain.api.company.validation.ValidCompanyEntityPortfolioTradeValidator.message=Company/Entity/Portfolio/Trade combination does not exist
com.solum.xplain.api.company.validation.ValidCompanyIpvDataGroups.message=Valuation Data groups are not valid
com.solum.xplain.api.company.validation.ValidCompanyMarketDataGroup.message=Market Data group is not valid
com.solum.xplain.api.portfolio.validation.ValidCalculationMarketDataGroup.message=Calculation market data group is not valid
com.solum.xplain.api.common.validation.ValidMarketDataGroupId.message=Market Data group is not valid
com.solum.xplain.api.curvegroup.validation.ValidFxRateCurrencies.message=FX rates pair is not valid!
com.solum.xplain.api.common.validation.ValidCurveDiscountingForm.message=Only Local Ccy allowed when Single
com.solum.xplain.api.common.validation.ValidFxSkewDeltas.message=Delta 1 should not be equal to Delta 2 and Delta 1 must be less than Delta 2
# Portfolio
com.solum.xplain.api.portfolio.validation.RequiredNonFixedLeg.message=IRS must not contain both fixed legs
com.solum.xplain.api.portfolio.validation.ExpiryDateBeforePaymentDate.message=Expiry date must be before payment date
com.solum.xplain.api.portfolio.validation.ValidAccrualMethod.message=Invalid Fixed leg accrual method
#Trades
com.solum.xplain.api.portfolio.validation.RequiredValidSwaptionLegs.message=One of the legs must be Fixed and the other one - Ibor or Overnight.
com.solum.xplain.api.portfolio.validation.ValidCallPut.message=Invalid Put/Call selection for base and counter currency notional signs
com.solum.xplain.api.portfolio.validation.ValidIborLegAccrualFrequency.message=Accrual Frequency must come from IBOR index attributes.
com.solum.xplain.api.portfolio.validation.ValidReferenceTradeId.message=Reference Security is not valid
com.solum.xplain.api.portfolio.validation.ValidAllocationTrade.positionNotNull=Position must not be null.
com.solum.xplain.api.portfolio.validation.ValidAllocationTrade.positionNull=Position must be null.
com.solum.xplain.api.portfolio.validation.externalTradeId.message=Trade external id must be unique
com.solum.xplain.api.portfolio.validation.UniqueExternalTradeIdSources.message=External Trade Id Source must be unique
com.solum.xplain.api.portfolio.validation.UniqueCustomTradeField.message=Custom Trade Field must be unique
com.solum.xplain.api.portfolio.validation.RequiredDifferentAmountSigns.message=Base and Counter currency amounts must have different signs
com.solum.xplain.api.portfolio.validation.ValidOtherOptionCounterNotionalValidator.message=First and Second Option Counter Notionals must have different signs
com.solum.xplain.api.portfolio.validation.RequiredValidNearFarDateNotionalSigns.message=Notional amounts in the same currency (base, notional) must have different signs across near and far dates 
com.solum.xplain.api.portfolio.validation.RequiredSameBaseCurrencyNotionals.message=Notional amounts for base currency must have the same magnitude
com.solum.xplain.api.portfolio.validation.RequiredDifferentFxLegIdentifiers.message=Leg identifiers must be unique within the same trade
com.solum.xplain.api.portfolio.validation.RequiredValidNearFarDateOrder.message=Far date must be after near date
com.solum.xplain.api.portfolio.validation.RequiredDifferentFxSwapLegIdentifiers.message=Leg identifiers must be unique within the same trade
com.solum.xplain.api.portfolio.validation.RequiredValidFraLegs.message = "FRA can only contain Fixed and Ibor legs"
com.solum.xplain.api.portfolio.validation.RequiredValidSameDayCount.message = "Day count must be the same on both legs"
com.solum.xplain.core.portfolio.validation.GreaterThanStartDate=Value must be greater than 'Start Date'.
com.solum.xplain.core.portfolio.validation.LessThanEndDate=Value must be less than 'End Date'.
com.solum.xplain.core.portfolio.validation.GreaterThanRegularStartDate=Value must be greater than 'Regular Start Date'.
com.solum.xplain.core.portfolio.validation.XccyDifferentCurrenciesRequired=Different currencies required
com.solum.xplain.core.portfolio.validation.XccyTradeCurrencyMustMatchLegCurrencies=Trade currency must match one of leg currencies
#Products
com.solum.xplain.api.tasks.validation.ValidProductsSize.message=Collection must contain all products
#Configuration
com.solum.xplain.api.curveconfiguration.validation.UniqueCurveConfigurationName.message=Curve configuration already exists with the same name!
com.solum.xplain.api.curveconfiguration.validation.ValidProviderOverrideFields.message=Invalid instrument override attributes!
com.solum.xplain.api.curveconfiguration.validation.ValidConfigurationInstrumentSize.message=Curve configuration must contain all instruments
#Dashboard
com.solum.xplain.api.dashboards.forms.UniqueDashboard.MarketData.message=Dashboard for this date and market data group already exists
com.solum.xplain.api.dashboards.forms.UniqueDashboard.Both.message=Dashboard for this date and market data group or portfolio already exists
com.solum.xplain.api.dashboards.forms.ValidDashboard.NotSingleDayRange.message=Date Range start and end dates must match
com.solum.xplain.api.dashboards.forms.ValidDashboard.EndBeforeStart.message=Date range end date must be equal or after start date
com.solum.xplain.api.dashboards.forms.ValidDashboard.MarketDataGroupId.message=At least one market data group must be selected
com.solum.xplain.api.common.validation.ValidAllowedTeamsForm.message=At least one team should be selected
com.solum.xplain.api.common.validation.ValidAllowedCompaniesForm.message=At least one company should be selected
com.solum.xplain.api.settings.validation.ValidGlobalSettingsMarketDataGroup.message="Market data group is not shared among all companies!
com.solum.xplain.api.company.validation.ValidCompanyTeams.message="Team is not allowed!"
#Tasks
com.solum.xplain.api.tasks.validation.UniqueTaskOverrides.message="Overrides have overlapping filters!"
com.solum.xplain.api.tasks.validation.ValidTaskAssetClassCount.message=Task definition must contain all asset classes
#Company
com.solum.xplain.api.company.validation.UniqueCompanyEntityExternalId.message=External id must be unique
com.solum.xplain.api.company.validation.UniqueCompanyExternalId.message=External id must be unique
com.solum.xplain.api.company.validation.UniqueCSAEntityExternalId.message=External id must be unique
#Company Documents
com.solum.xplain.api.companydocs.validation.ValidDocumentSize.message=Maximum file size is 100MB
com.solum.xplain.api.companydocs.validation.ValidDocumentType.message=Only Microsoft Office (doc/docx/xls/xlsx/ppt/pptx), Open Office (odt/odp/odx), JPEG (jpeg, jpg), Text, PNG and PDF file extensions allowed
com.solum.xplain.api.companydocs.validation.ValidDocumentType.contentTypeNoMatch=The file format and extension don't match
#Market
com.solum.xplain.api.market.validation.ValidMarketDataKeyInstrument.message=Invalid asset class instrument
com.solum.xplain.api.market.validation.ValidFxRateKey.message=FX Rate key is not valid
#Correlation Matrix
com.solum.xplain.api.correlation.validation.ValidMatrixNode.message="Only value 1 is allowed for same correlation key"
com.solum.xplain.api.correlation.validation.UniqueCorrelationMatrixName.message="Correlation matrix with the same name already exists!"
#Fixing
com.solum.xplain.api.fixings.validation.UniqueFixing.message=Duplicate fixings are not allowed
com.solum.xplain.api.fixings.validation.ValidPublicationDate.message=Publication date only valid for Inflation fixings
com.solum.xplain.api.fixings.validation.ValidFixingDate.message=Inflation fixing must have end of month date
#Common
com.solum.xplain.api.common.validation.ValidStringSet.message=Invalid string value
com.solum.xplain.api.common.validation.ValidCollectionStringSet.message=Invalid string collection
com.solum.xplain.api.common.validation.ValidObjectId.message=Invalid object id
com.solum.xplain.api.common.validation.NotZero.message=Value cannot be equal to zero
#TRS
com.solum.xplain.api.trs.valuation.validation.ValidTrsValuationMarketDataGroup.message=Valuation market data group is not valid
com.solum.xplain.trs.portfolio.trade.validation.RequiredDifferentLegIdentifiers.message=Leg identifiers must be unique within the same trade
com.solum.xplain.trs.index.validation.UniqueTrsIndexValidator.message=TRS Index name must be unique
com.solum.xplain.trs.portfolio.trade.validation.externalTradeId.message=Trade external id must be unique
com.solum.xplain.trs.portfolio.trade.validation.RequiredEqualAccrualPaymentFrequencies.message=Leg must have equal payment and accrual frequencies
#Audit
com.solum.xplain.core.accesslog.validation.ValidAccessLogFilter.message=Access Log Filter must not be empty
#Loan Note
com.solum.xplain.core.portfolio.form.loanNote.businessDayConvention=Only \"NoAdjust\" value is allowed.
com.solum.xplain.core.portfolio.form.loanNote.businessDayAdjustmentType=Only \"ACCRUAL_AND_PAYMENT\" value is allowed.
# XVA
com.solum.xplain.xva.calculation.validation.ValidCalculationCorrelationMatrix.message=Correlation matrix currency must match discount currency
# Simulation
com.solum.xplain.api.calculation.simulation.validation.UniqueSimulationName.message=Simulation with this name already exists
com.solum.xplain.xva.settings.validation.ValidIborIndexCurrency.message=Invalid ibor index for currency
com.solum.xplain.api.calculation.simulation.ccyexposure.validation.ValidSimulationCalculationCurrencies.message=Base Currency and Counter Currency Pair combination not valid
com.solum.xplain.calculation.exposure.validation.HorizonStartDateBeforeHorizonEndDate.message=The horizon end date should be after the horizon start date
# PnL Explain Calculations
com.solum.xplain.api.calculation.pnl.validation.ValidPnlValuationDates.message=Second Valuation Date must be after First Valuation Date
com.solum.xplain.api.portfolio.validation.ValidMidPriceRequirements.message=All Price Requirements must be set to Mid
# View Configuration
com.solum.xplain.core.viewconfig.validation.UniqueViewConfiguration.message=View Configuration with this name and view class already exists
com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupForm.hexColour.pattern=Invalid RGB hex colour
# XM
com.solum.xplain.xm.excmngmt.processipv.validation.ValidXmClassifierStringSet.message=Invalid string value
com.solum.xplain.xm.settings.validation.ValidOnboardingPeriod.message=Onboarding period permissible values are strictly positive integers.
