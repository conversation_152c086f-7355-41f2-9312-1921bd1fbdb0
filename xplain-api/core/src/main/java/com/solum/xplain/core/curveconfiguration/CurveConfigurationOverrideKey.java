package com.solum.xplain.core.curveconfiguration;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.extensions.enums.CreditSector;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveConfigurationOverrideKey {

  private final String instrumentName;
  private final String assetName;
  private final String currencyPair;
  private final CreditSector creditSector;

  public static CurveConfigurationOverrideKey key(InstrumentDefinition definition) {
    if (StringUtils.isNotEmpty(definition.getFxPair())) {
      return currencyPairOverrideKey(definition.getInstrument(), definition.getFxPair());
    }

    return new CurveConfigurationOverrideKey(
        definition.getInstrument().name(), definition.getAssetName(), null, definition.getSector());
  }

  public static CurveConfigurationOverrideKey sectorOverrideKey(
      InstrumentType instrumentType, CreditSector sector) {
    return new CurveConfigurationOverrideKey(instrumentType.name(), null, null, sector);
  }

  public static CurveConfigurationOverrideKey assetNameOverrideKey(
      InstrumentType instrumentType, String assetName) {
    return new CurveConfigurationOverrideKey(instrumentType.name(), assetName, null, null);
  }

  public static CurveConfigurationOverrideKey currencyPairOverrideKey(
      InstrumentType instrumentType, String currencyPair) {
    return new CurveConfigurationOverrideKey(instrumentType.name(), null, currencyPair, null);
  }

  public String getInstrumentName() {
    return instrumentName;
  }

  public String getAssetName() {
    return assetName;
  }

  public String getCurrencyPair() {
    return currencyPair;
  }

  public CreditSector getCreditSector() {
    return creditSector;
  }
}
