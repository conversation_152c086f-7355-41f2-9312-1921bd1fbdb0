package com.solum.xplain.core.curveconfiguration.csv.configuration;

import static com.solum.xplain.core.curveconfiguration.csv.configuration.CurveConfigurationCsvLoader.CURVE_GROUP_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.CONFIGURATION_NAME_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.INSTRUMENT_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.PRIMARY_PROVIDER_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.SECONDARY_PROVIDER_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.instrument.InstrumentType;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurveConfigurationCsvMapper {

  public static final List<String> CURVE_CONFIGURATION_CSV_HEADER =
      List.of(
          CONFIGURATION_NAME_FIELD,
          CURVE_GROUP_FIELD,
          INSTRUMENT_FIELD,
          PRIMARY_PROVIDER_FIELD,
          SECONDARY_PROVIDER_FIELD);

  public static List<CsvRow> toCsvRows(CurveConfigurationView configuration) {
    var nameField = configuration.getName();
    var curveGroupField = configuration.getCurveGroupName();

    return configuration.getInstruments().entrySet().stream()
        .filter(i -> i.getValue().getPrimary() != null)
        .sorted(Comparator.comparing(v -> v.getKey().name()))
        .map(i -> toCsvRow(nameField, curveGroupField, i))
        .toList();
  }

  private static CsvRow toCsvRow(
      String name,
      String curveGroup,
      Map.Entry<InstrumentType, CurveConfigurationProviderView> instruments) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(CONFIGURATION_NAME_FIELD, name));
    builder.add(new CsvField(CURVE_GROUP_FIELD, curveGroup));
    builder.add(new CsvField(INSTRUMENT_FIELD, instruments.getKey().getLabel()));
    builder.add(new CsvField(PRIMARY_PROVIDER_FIELD, instruments.getValue().getPrimary()));

    if (instruments.getValue().getSecondary() != null) {
      builder.add(new CsvField(SECONDARY_PROVIDER_FIELD, instruments.getValue().getSecondary()));
    }
    return new CsvRow(builder.build());
  }
}
