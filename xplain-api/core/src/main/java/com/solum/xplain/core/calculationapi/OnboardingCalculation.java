package com.solum.xplain.core.calculationapi;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.bson.types.ObjectId;

public interface OnboardingCalculation {

  Either<ErrorItem, EntityId> calculate(
      ObjectId onboardingReportId,
      LocalDate valuationDate,
      AuditUser createdBy,
      PortfolioSettings<CompanyLegalEntitySettingsView> settings,
      MarketDataSourceType sourceType,
      BitemporalDate stateDate);
}
