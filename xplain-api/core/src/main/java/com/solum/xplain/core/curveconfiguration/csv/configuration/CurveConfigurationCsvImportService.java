package com.solum.xplain.core.curveconfiguration.csv.configuration;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.BaseVersionedImportService;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import jakarta.inject.Provider;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveConfigurationCsvImportService
    extends BaseVersionedImportService<CurveConfigurationForm, String, CurveConfiguration> {

  private final CurveConfigurationRepository curveConfigRepository;
  private final Provider<CurveConfigurationCsvLoader> loaderProvider;

  public CurveConfigurationCsvImportService(
      AuditEntryService auditEntryService,
      CurveConfigurationRepository curveConfigRepository,
      Provider<CurveConfigurationCsvLoader> loaderProvider) {
    super(auditEntryService);
    this.curveConfigRepository = curveConfigRepository;
    this.loaderProvider = loaderProvider;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importCurveConfigurations(
      ImportOptions importOptions, byte[] bytes) {
    return loaderProvider
        .get()
        .parse(bytes, importOptions.parsingMode())
        .map(parsed -> importCurveConfig(parsed, importOptions))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            importResult -> toReturn(importOptions.getDuplicateAction(), importResult));
  }

  private ImportResult importCurveConfig(
      CsvParserResult<CurveConfigurationForm> parserResult, ImportOptions importOptions) {
    var importItems = buildImportItems(importOptions.getStateDate(), parserResult.getParsedLines());
    var importLogs = importItems(importOptions, importItems);
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private ImportItems<CurveConfigurationForm, String, CurveConfiguration> buildImportItems(
      LocalDate stateDate, List<CurveConfigurationForm> forms) {
    var existingItems = curveConfigRepository.findAllActiveItems(stateDate);
    return ImportItems.<CurveConfigurationForm, String, CurveConfiguration>builder()
        .existingActiveItems(existingItems)
        .existingItemToKeyFn(CurveConfiguration::getName)
        .importItems(forms)
        .importItemToKeyFn(CurveConfigurationForm::getName)
        .build();
  }

  @Override
  protected String getIdentifier(String key) {
    return key;
  }

  @Override
  protected Either<ErrorItem, EntityId> insert(String parentId, CurveConfigurationForm form) {
    return curveConfigRepository.insert(form);
  }

  @Override
  protected Either<ErrorItem, EntityId> update(
      CurveConfiguration entity, CurveConfigurationForm form) {
    return curveConfigRepository.update(entity.getEntityId(), entity.getValidFrom(), form);
  }

  @Override
  protected Either<ErrorItem, EntityId> archive(CurveConfiguration entity, ArchiveEntityForm f) {
    return curveConfigRepository.archive(entity.getEntityId(), entity.getValidFrom(), f);
  }

  @Override
  protected boolean hasFutureVersions(String groupId, String key, LocalDate stateDate) {
    var searchForm = new CurveConfigurationSearchForm(key, stateDate);
    return !curveConfigRepository.futureVersions(searchForm).getDates().isEmpty();
  }

  @Override
  protected String getCollection() {
    return CurveConfiguration.CURVE_CONFIGURATION_COLLECTION;
  }

  @Override
  protected String getObjectName() {
    return "Curve configurations";
  }
}
