package com.solum.xplain.core.customfield;

import com.solum.xplain.core.customfield.repository.fragments.CustomFieldNameQueries;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomFieldNameRepository
    extends org.springframework.data.repository.Repository<CustomFieldName, String>,
        CustomFieldNameQueries {
  // TODO pull in the methods from CustomFieldNameQueries as Spring Data methods
}
