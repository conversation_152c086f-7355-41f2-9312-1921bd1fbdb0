package com.solum.xplain.core.customfield.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueCustomFieldNameValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface UniqueCustomFieldName {
  String message() default "{UniqueExternalId}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
