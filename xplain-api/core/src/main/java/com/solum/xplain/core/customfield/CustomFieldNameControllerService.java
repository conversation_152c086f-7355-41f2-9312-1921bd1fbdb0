package com.solum.xplain.core.customfield;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.customfield.value.CustomFieldNameCreateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameUpdateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class CustomFieldNameControllerService {

  private final CustomFieldNameRepository repository;

  public List<CustomFieldNameView> list(TableFilter tableFilter, Sort sort, boolean archived) {
    return repository.list(tableFilter, sort, archived);
  }

  public Either<ErrorItem, EntityId> insert(CustomFieldNameCreateForm form) {
    return repository.insert(form);
  }

  public Either<ErrorItem, EntityId> update(String id, CustomFieldNameUpdateForm form) {
    return repository.update(id, form);
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return repository.archive(id);
  }
}
