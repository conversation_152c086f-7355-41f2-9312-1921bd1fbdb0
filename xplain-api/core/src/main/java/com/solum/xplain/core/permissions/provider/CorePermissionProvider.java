package com.solum.xplain.core.permissions.provider;

import static com.solum.xplain.core.permissions.CorePermissionCategory.AUDIT_ENTRIES;
import static com.solum.xplain.core.permissions.CorePermissionCategory.BREAK_TESTS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CALENDARS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CCY_EXPOSURE;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CCY_EXPOSURE_SIMULATIONS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.COMPANY_LIST;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CONVENTION_OVERRIDES;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CURVE_CONFIGURATIONS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CURVE_GROUPS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.CUSTOM_ATTRIBUTES;
import static com.solum.xplain.core.permissions.CorePermissionCategory.DASHBOARD;
import static com.solum.xplain.core.permissions.CorePermissionCategory.DATA_PROVIDERS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.FIXINGS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.MARKET_DATA;
import static com.solum.xplain.core.permissions.CorePermissionCategory.MARKET_DATA_KEYS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.MD_TASK_EXECUTION;
import static com.solum.xplain.core.permissions.CorePermissionCategory.PORTFOLIO_LIST;
import static com.solum.xplain.core.permissions.CorePermissionCategory.PV_CALCULATIONS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.ROLES;
import static com.solum.xplain.core.permissions.CorePermissionCategory.TASK_MANAGEMENT;
import static com.solum.xplain.core.permissions.CorePermissionCategory.TEAMS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.VALUATION_DATA;
import static com.solum.xplain.core.permissions.CorePermissionCategory.VALUATION_SETTINGS;
import static com.solum.xplain.core.permissions.CorePermissionCategory.VD_MANUAL_OVERRIDE;
import static com.solum.xplain.core.permissions.CorePermissionCategory.VD_TASK_EXECUTION;
import static com.solum.xplain.core.permissions.CorePermissionCategory.VIEW_CONFIG_SETTINGS;
import static com.solum.xplain.core.permissions.UserType.ADMIN_USER;
import static com.solum.xplain.core.permissions.UserType.BASIC_USER;
import static com.solum.xplain.core.permissions.UserType.SUPER_USER;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.permissions.extension.XplainPermission;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CorePermissionProvider implements PermissionsProvider {

  // Roles
  private static final XplainPermission VIEW_ROLE =
      new XplainPermission("VIEW_ROLE", Authorities.VIEW_ROLE, BASIC_USER, ROLES);
  private static final XplainPermission MODIFY_ROLE =
      new XplainPermission("MODIFY_ROLE", Authorities.MODIFY_ROLE, ADMIN_USER, ROLES);

  // Teams
  private static final XplainPermission MODIFY_TEAM =
      new XplainPermission("MODIFY_TEAM", Authorities.MODIFY_TEAM, ADMIN_USER, TEAMS);
  private static final XplainPermission VIEW_TEAM =
      new XplainPermission("VIEW_TEAM", Authorities.VIEW_TEAM, BASIC_USER, TEAMS);

  // Fixings
  private static final XplainPermission MODIFY_FIXING =
      new XplainPermission("MODIFY_FIXING", Authorities.MODIFY_FIXING, ADMIN_USER, FIXINGS);
  private static final XplainPermission VIEW_FIXING =
      new XplainPermission("VIEW_FIXING", Authorities.VIEW_FIXING, BASIC_USER, FIXINGS);

  // Global Settings
  private static final XplainPermission MODIFY_VALUATION_SETTINGS =
      new XplainPermission(
          "MODIFY_VALUATION_SETTINGS",
          Authorities.MODIFY_VALUATION_SETTINGS,
          ADMIN_USER,
          VALUATION_SETTINGS);
  private static final XplainPermission VIEW_VALUATION_SETTINGS =
      new XplainPermission(
          "VIEW_VALUATION_SETTINGS",
          Authorities.VIEW_VALUATION_SETTINGS,
          BASIC_USER,
          VALUATION_SETTINGS);

  // Data Providers
  private static final XplainPermission MODIFY_DATA_PROVIDER =
      new XplainPermission(
          "MODIFY_DATA_PROVIDER", Authorities.MODIFY_DATA_PROVIDER, ADMIN_USER, DATA_PROVIDERS);
  private static final XplainPermission VIEW_DATA_PROVIDER =
      new XplainPermission(
          "VIEW_DATA_PROVIDER", Authorities.VIEW_DATA_PROVIDER, BASIC_USER, DATA_PROVIDERS);

  // Data Providers
  private static final XplainPermission MODIFY_IDENTIFIER_SOURCE =
      new XplainPermission(
          "MODIFY_IDENTIFIER_SOURCE",
          Authorities.MODIFY_IDENTIFIER_SOURCE,
          ADMIN_USER,
          DATA_PROVIDERS);
  private static final XplainPermission VIEW_IDENTIFIER_SOURCE =
      new XplainPermission(
          "VIEW_IDENTIFIER_SOURCE", Authorities.VIEW_IDENTIFIER_SOURCE, BASIC_USER, DATA_PROVIDERS);

  // Ccy Exposures
  private static final XplainPermission MODIFY_CCY_EXPOSURE =
      new XplainPermission(
          "MODIFY_CCY_EXPOSURE", Authorities.MODIFY_CCY_EXPOSURE, ADMIN_USER, CCY_EXPOSURE);
  private static final XplainPermission VIEW_CCY_EXPOSURE =
      new XplainPermission(
          "VIEW_CCY_EXPOSURE", Authorities.VIEW_CCY_EXPOSURE, BASIC_USER, CCY_EXPOSURE);

  // Custom field names
  private static final XplainPermission MODIFY_CUSTOM_FIELD_NAME =
      new XplainPermission(
          "MODIFY_CUSTOM_FIELD_NAME",
          Authorities.MODIFY_CUSTOM_FIELD_NAME,
          ADMIN_USER,
          CUSTOM_ATTRIBUTES);
  private static final XplainPermission VIEW_CUSTOM_FIELD_NAME =
      new XplainPermission(
          "VIEW_CUSTOM_FIELD_NAME",
          Authorities.VIEW_CUSTOM_FIELD_NAME,
          BASIC_USER,
          CUSTOM_ATTRIBUTES);

  // Market Data Key
  private static final XplainPermission VIEW_MARKET_DATA_KEY =
      new XplainPermission(
          "VIEW_MARKET_DATA_KEY", Authorities.VIEW_MARKET_DATA_KEY, BASIC_USER, MARKET_DATA_KEYS);
  private static final XplainPermission MODIFY_MARKET_DATA_KEY =
      new XplainPermission(
          "MODIFY_MARKET_DATA_KEY",
          Authorities.MODIFY_MARKET_DATA_KEY,
          ADMIN_USER,
          MARKET_DATA_KEYS);
  private static final XplainPermission CONFIGURE_MARKET_DATA_KEY =
      new XplainPermission(
          "CONFIGURE_MARKET_DATA_KEY",
          Authorities.CONFIGURE_MARKET_DATA_KEY,
          SUPER_USER,
          MARKET_DATA_KEYS);

  // Calendars
  private static final XplainPermission MODIFY_CALENDAR =
      new XplainPermission("MODIFY_CALENDAR", Authorities.MODIFY_CALENDAR, ADMIN_USER, CALENDARS);
  private static final XplainPermission VIEW_CALENDAR =
      new XplainPermission("VIEW_CALENDAR", Authorities.VIEW_CALENDAR, BASIC_USER, CALENDARS);

  // Convention Overrides
  private static final XplainPermission MODIFY_CONVENTION_OVERRIDE =
      new XplainPermission(
          "MODIFY_CONVENTION_OVERRIDE",
          Authorities.MODIFY_CONVENTION_OVERRIDE,
          ADMIN_USER,
          CONVENTION_OVERRIDES);
  private static final XplainPermission VIEW_CONVENTION_OVERRIDE =
      new XplainPermission(
          "VIEW_CONVENTION_OVERRIDE",
          Authorities.VIEW_CONVENTION_OVERRIDE,
          ADMIN_USER,
          CONVENTION_OVERRIDES);

  // TasksController
  private static final XplainPermission MODIFY_TASK_DEFINITION =
      new XplainPermission(
          "MODIFY_TASK_DEFINITION",
          Authorities.MODIFY_TASK_DEFINITION,
          ADMIN_USER,
          TASK_MANAGEMENT);
  private static final XplainPermission VIEW_TASK_DEFINITION =
      new XplainPermission(
          "VIEW_TASK_DEFINITION", Authorities.VIEW_TASK_DEFINITION, BASIC_USER, TASK_MANAGEMENT);

  // MD Task Executions
  private static final XplainPermission VIEW_MD_TASK_EXECUTION =
      new XplainPermission(
          "VIEW_MD_TASK_EXECUTION",
          Authorities.VIEW_MD_TASK_EXECUTION,
          BASIC_USER,
          MD_TASK_EXECUTION);
  private static final XplainPermission RUN_MD_TASK_EXECUTION =
      new XplainPermission(
          "RUN_MD_TASK_EXECUTION",
          Authorities.RUN_MD_TASK_EXECUTION,
          SUPER_USER,
          MD_TASK_EXECUTION);

  // IpvTasksController
  private static final XplainPermission VIEW_VD_TASK_DEFINITION =
      new XplainPermission(
          "VIEW_VD_TASK_DEFINITION",
          Authorities.VIEW_VD_TASK_DEFINITION,
          BASIC_USER,
          TASK_MANAGEMENT);
  private static final XplainPermission MODIFY_VD_TASK_DEFINITION =
      new XplainPermission(
          "MODIFY_VD_TASK_DEFINITION",
          Authorities.MODIFY_VD_TASK_DEFINITION,
          ADMIN_USER,
          TASK_MANAGEMENT);

  // VD Task Executions
  private static final XplainPermission VIEW_VD_TASK_EXECUTION =
      new XplainPermission(
          "VIEW_VD_TASK_EXECUTION",
          Authorities.VIEW_VD_TASK_EXECUTION,
          BASIC_USER,
          VD_TASK_EXECUTION);
  private static final XplainPermission RUN_VD_TASK_EXECUTION =
      new XplainPermission(
          "RUN_VD_TASK_EXECUTION",
          Authorities.RUN_VD_TASK_EXECUTION,
          SUPER_USER,
          VD_TASK_EXECUTION);

  // CurveGroupController
  private static final XplainPermission VIEW_CURVE_GROUP =
      new XplainPermission(
          "VIEW_CURVE_GROUP", Authorities.VIEW_CURVE_GROUP, BASIC_USER, CURVE_GROUPS);
  private static final XplainPermission MODIFY_CURVE_GROUP =
      new XplainPermission(
          "MODIFY_CURVE_GROUP", Authorities.MODIFY_CURVE_GROUP, ADMIN_USER, CURVE_GROUPS);

  // CurveGroupCurveController
  private static final XplainPermission VIEW_CURVE =
      new XplainPermission("VIEW_CURVE", Authorities.VIEW_CURVE, BASIC_USER, CURVE_GROUPS);
  private static final XplainPermission MODIFY_CURVE =
      new XplainPermission("MODIFY_CURVE", Authorities.MODIFY_CURVE, ADMIN_USER, CURVE_GROUPS);

  // CurveGroupCreditCurveController
  private static final XplainPermission VIEW_CREDIT_CURVE =
      new XplainPermission(
          "VIEW_CREDIT_CURVE", Authorities.VIEW_CREDIT_CURVE, BASIC_USER, CURVE_GROUPS);
  private static final XplainPermission MODIFY_CREDIT_CURVE =
      new XplainPermission(
          "MODIFY_CREDIT_CURVE", Authorities.MODIFY_CREDIT_CURVE, ADMIN_USER, CURVE_GROUPS);

  // BondCurveController
  private static final XplainPermission VIEW_BOND_CURVE =
      new XplainPermission(
          "VIEW_BOND_CURVE", Authorities.VIEW_BOND_CURVE, BASIC_USER, CURVE_GROUPS);
  private static final XplainPermission MODIFY_BOND_CURVE =
      new XplainPermission(
          "MODIFY_BOND_CURVE", Authorities.MODIFY_BOND_CURVE, ADMIN_USER, CURVE_GROUPS);

  // CurveGroupFxRateController
  private static final XplainPermission VIEW_FX_RATE =
      new XplainPermission("VIEW_FX_RATE", Authorities.VIEW_FX_RATE, BASIC_USER, CURVE_GROUPS);
  private static final XplainPermission MODIFY_FX_RATE =
      new XplainPermission("MODIFY_FX_RATE", Authorities.MODIFY_FX_RATE, ADMIN_USER, CURVE_GROUPS);

  // CurveGroupFxVolController
  private static final XplainPermission MODIFY_FX_VOL =
      new XplainPermission("MODIFY_FX_VOL", Authorities.MODIFY_FX_VOL, ADMIN_USER, CURVE_GROUPS);
  private static final XplainPermission VIEW_FX_VOL =
      new XplainPermission("VIEW_FX_VOL", Authorities.VIEW_FX_VOL, BASIC_USER, CURVE_GROUPS);

  // CurveGroupVolatilityController
  private static final XplainPermission VIEW_VOL_SURFACE =
      new XplainPermission(
          "VIEW_VOL_SURFACE", Authorities.VIEW_VOL_SURFACE, BASIC_USER, CURVE_GROUPS);
  private static final XplainPermission MODIFY_VOL_SURFACE =
      new XplainPermission(
          "MODIFY_VOL_SURFACE", Authorities.MODIFY_VOL_SURFACE, ADMIN_USER, CURVE_GROUPS);

  // Curve Configuration
  private static final XplainPermission VIEW_CURVE_CONFIG =
      new XplainPermission(
          "VIEW_CURVE_CONFIG", Authorities.VIEW_CURVE_CONFIG, BASIC_USER, CURVE_CONFIGURATIONS);
  private static final XplainPermission MODIFY_CURVE_CONFIG =
      new XplainPermission(
          "MODIFY_CURVE_CONFIG", Authorities.MODIFY_CURVE_CONFIG, ADMIN_USER, CURVE_CONFIGURATIONS);
  private static final XplainPermission RUN_CURVE_CONFIG =
      new XplainPermission(
          "RUN_CURVE_CONFIG", Authorities.RUN_CURVE_CONFIG, SUPER_USER, CURVE_CONFIGURATIONS);

  // Company
  private static final XplainPermission VIEW_COMPANY =
      new XplainPermission("VIEW_COMPANY", Authorities.VIEW_COMPANY, BASIC_USER, COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY =
      new XplainPermission("MODIFY_COMPANY", Authorities.MODIFY_COMPANY, ADMIN_USER, COMPANY_LIST);

  private static final XplainPermission VIEW_COMPANY_DOCS =
      new XplainPermission(
          "VIEW_COMPANY_DOCS", Authorities.VIEW_COMPANY_DOCUMENTS, BASIC_USER, COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY_DOCS =
      new XplainPermission(
          "MODIFY_COMPANY_DOCS", Authorities.MODIFY_COMPANY_DOCUMENTS, ADMIN_USER, COMPANY_LIST);

  // Company VD Settings
  private static final XplainPermission VIEW_COMPANY_VD_SETTINGS =
      new XplainPermission(
          "VIEW_COMPANY_VD_SETTINGS",
          Authorities.VIEW_COMPANY_VD_SETTINGS,
          BASIC_USER,
          COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY_VD_SETTINGS =
      new XplainPermission(
          "MODIFY_COMPANY_VD_SETTINGS",
          Authorities.MODIFY_COMPANY_VD_SETTINGS,
          ADMIN_USER,
          COMPANY_LIST);

  // Company Legal entity VD Settings
  private static final XplainPermission VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS =
      new XplainPermission(
          "VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS",
          Authorities.VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS,
          BASIC_USER,
          COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS =
      new XplainPermission(
          "MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS",
          Authorities.MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS,
          ADMIN_USER,
          COMPANY_LIST);

  // Company Legal Entity
  private static final XplainPermission VIEW_COMPANY_LEGAL_ENTITY =
      new XplainPermission(
          "VIEW_COMPANY_LEGAL_ENTITY",
          Authorities.VIEW_COMPANY_LEGAL_ENTITY,
          BASIC_USER,
          COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY_LEGAL_ENTITY =
      new XplainPermission(
          "MODIFY_COMPANY_LEGAL_ENTITY",
          Authorities.MODIFY_COMPANY_LEGAL_ENTITY,
          ADMIN_USER,
          COMPANY_LIST);

  // Company Valuation settings
  private static final XplainPermission VIEW_COMPANY_VALUATION_SETTINGS =
      new XplainPermission(
          "VIEW_COMPANY_VALUATION_SETTINGS",
          Authorities.VIEW_COMPANY_VALUATION_SETTINGS,
          BASIC_USER,
          COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY_VALUATION_SETTINGS =
      new XplainPermission(
          "MODIFY_COMPANY_VALUATION_SETTINGS",
          Authorities.MODIFY_COMPANY_VALUATION_SETTINGS,
          ADMIN_USER,
          COMPANY_LIST);

  // Company Legal Entity Valuation settings
  private static final XplainPermission VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS =
      new XplainPermission(
          "VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS",
          Authorities.VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS,
          BASIC_USER,
          COMPANY_LIST);
  private static final XplainPermission MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS =
      new XplainPermission(
          "MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS",
          Authorities.MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS,
          ADMIN_USER,
          COMPANY_LIST);

  // Portfolios and Trades
  private static final XplainPermission VIEW_PORTFOLIO =
      new XplainPermission(
          "VIEW_PORTFOLIO", Authorities.VIEW_PORTFOLIO, BASIC_USER, PORTFOLIO_LIST);
  private static final XplainPermission MODIFY_PORTFOLIO =
      new XplainPermission(
          "MODIFY_PORTFOLIO", Authorities.MODIFY_PORTFOLIO, ADMIN_USER, PORTFOLIO_LIST);
  private static final XplainPermission MODIFY_TRADE =
      new XplainPermission("MODIFY_TRADE", Authorities.MODIFY_TRADE, ADMIN_USER, PORTFOLIO_LIST);
  private static final XplainPermission RUN_PV_CALCULATIONS =
      new XplainPermission(
          "RUN_PV_CALCULATIONS", Authorities.RUN_PV_CALCULATIONS, SUPER_USER, PV_CALCULATIONS);
  private static final XplainPermission VIEW_CALCULATION_RESULTS =
      new XplainPermission(
          "VIEW_CALCULATION_RESULTS",
          Authorities.VIEW_CALCULATION_RESULTS,
          BASIC_USER,
          PV_CALCULATIONS);
  private static final XplainPermission VIEW_CCY_EXPOSURE_SIM_RESULTS =
      new XplainPermission(
          "VIEW_CCY_EXPOSURE_SIM_RESULTS",
          Authorities.VIEW_CCY_EXPOSURE_SIM,
          BASIC_USER,
          CCY_EXPOSURE_SIMULATIONS);
  private static final XplainPermission RUN_CCY_EXPOSURE_SIM =
      new XplainPermission(
          "RUN_CCY_EXPOSURE_SIM",
          Authorities.RUN_CCY_EXPOSURE_SIM,
          SUPER_USER,
          CCY_EXPOSURE_SIMULATIONS);

  private static final XplainPermission VIEW_TRADE =
      new XplainPermission("VIEW_TRADE", Authorities.VIEW_TRADE, BASIC_USER, PORTFOLIO_LIST);

  // Market Data Group
  private static final XplainPermission VIEW_MARKET_DATA_GROUP =
      new XplainPermission(
          "VIEW_MARKET_DATA_GROUP", Authorities.VIEW_MARKET_DATA_GROUP, BASIC_USER, MARKET_DATA);
  private static final XplainPermission MODIFY_MARKET_DATA_GROUP =
      new XplainPermission(
          "MODIFY_MARKET_DATA_GROUP",
          Authorities.MODIFY_MARKET_DATA_GROUP,
          ADMIN_USER,
          MARKET_DATA);

  // Market Data Values
  private static final XplainPermission MODIFY_MARKET_DATA =
      new XplainPermission(
          "MODIFY_MARKET_DATA", Authorities.MODIFY_MARKET_DATA, ADMIN_USER, MARKET_DATA);
  private static final XplainPermission VIEW_MARKET_DATA =
      new XplainPermission(
          "VIEW_MARKET_DATA", Authorities.VIEW_MARKET_DATA, BASIC_USER, MARKET_DATA);

  // Exception Management
  private static final XplainPermission VIEW_MD_EXC_MANAGEMENT_RESULT =
      new XplainPermission(
          "VIEW_MD_EXC_MANAGEMENT_RESULT",
          Authorities.VIEW_MD_EXC_MANAGEMENT_RESULT,
          BASIC_USER,
          MD_TASK_EXECUTION);
  private static final XplainPermission VIEW_VD_EXC_MANAGEMENT_RESULT =
      new XplainPermission(
          "VIEW_VD_EXC_MANAGEMENT_RESULT",
          Authorities.VIEW_VD_EXC_MANAGEMENT_RESULT,
          BASIC_USER,
          VD_TASK_EXECUTION);

  // Break Tests
  private static final XplainPermission VIEW_MD_BREAK_TEST =
      new XplainPermission(
          "VIEW_MD_BREAK_TEST", Authorities.VIEW_MD_BREAK_TEST, BASIC_USER, BREAK_TESTS);
  private static final XplainPermission MODIFY_MD_BREAK_TEST =
      new XplainPermission(
          "MODIFY_MD_BREAK_TEST", Authorities.MODIFY_MD_BREAK_TEST, ADMIN_USER, BREAK_TESTS);
  private static final XplainPermission CONFIGURE_MD_BREAK_TEST =
      new XplainPermission(
          "CONFIGURE_MD_BREAK_TEST", Authorities.CONFIGURE_MD_BREAK_TEST, SUPER_USER, BREAK_TESTS);

  // IPV Break Tests
  private static final XplainPermission VIEW_VD_BREAK_TEST =
      new XplainPermission(
          "VIEW_VD_BREAK_TEST", Authorities.VIEW_VD_BREAK_TEST, BASIC_USER, BREAK_TESTS);
  private static final XplainPermission MODIFY_VD_BREAK_TEST =
      new XplainPermission(
          "MODIFY_VD_BREAK_TEST", Authorities.MODIFY_VD_BREAK_TEST, ADMIN_USER, BREAK_TESTS);
  private static final XplainPermission CONFIGURE_VD_BREAK_TEST =
      new XplainPermission(
          "CONFIGURE_VD_BREAK_TEST", Authorities.CONFIGURE_VD_BREAK_TEST, SUPER_USER, BREAK_TESTS);

  // Onboarding Break Tests
  private static final XplainPermission VIEW_ON_BREAK_TEST =
      new XplainPermission(
          "VIEW_ON_BREAK_TEST", Authorities.VIEW_ON_BREAK_TEST, BASIC_USER, BREAK_TESTS);
  private static final XplainPermission MODIFY_ON_BREAK_TEST =
      new XplainPermission(
          "MODIFY_ON_BREAK_TEST", Authorities.MODIFY_ON_BREAK_TEST, ADMIN_USER, BREAK_TESTS);
  private static final XplainPermission CONFIGURE_ON_BREAK_TEST =
      new XplainPermission(
          "CONFIGURE_ON_BREAK_TEST", Authorities.CONFIGURE_ON_BREAK_TEST, SUPER_USER, BREAK_TESTS);

  // VD Data Group
  private static final XplainPermission VIEW_VALUATION_DATA_GROUP =
      new XplainPermission(
          "VIEW_VALUATION_DATA_GROUP",
          Authorities.VIEW_VALUATION_DATA_GROUP,
          BASIC_USER,
          VALUATION_DATA);
  private static final XplainPermission MODIFY_VALUATION_DATA_GROUP =
      new XplainPermission(
          "MODIFY_VALUATION_DATA_GROUP",
          Authorities.MODIFY_VALUATION_DATA_GROUP,
          ADMIN_USER,
          VALUATION_DATA);

  private static final XplainPermission MODIFY_VALUATION_DATA =
      new XplainPermission(
          "MODIFY_VALUATION_DATA", Authorities.MODIFY_VALUATION_DATA, ADMIN_USER, VALUATION_DATA);
  private static final XplainPermission VIEW_VALUATION_DATA =
      new XplainPermission(
          "VIEW_VALUATION_DATA", Authorities.VIEW_VALUATION_DATA, BASIC_USER, VALUATION_DATA);

  // Audit Entries
  private static final XplainPermission VIEW_AUDIT_ENTRY =
      new XplainPermission(
          "VIEW_AUDIT_ENTRY", Authorities.VIEW_AUDIT_ENTRY, BASIC_USER, AUDIT_ENTRIES);
  private static final XplainPermission VIEW_ACCESS_LOG =
      new XplainPermission(
          "VIEW_ACCESS_LOG", Authorities.VIEW_ACCESS_LOG, ADMIN_USER, AUDIT_ENTRIES);
  // Dashboard
  private static final XplainPermission VIEW_DASHBOARD =
      new XplainPermission("VIEW_DASHBOARD", Authorities.VIEW_DASHBOARD, BASIC_USER, DASHBOARD);
  private static final XplainPermission RUN_DASHBOARD =
      new XplainPermission("RUN_DASHBOARD", Authorities.RUN_DASHBOARD, SUPER_USER, DASHBOARD);

  // Onboarding Report
  private static final XplainPermission VIEW_ONBOARDING_REPORT =
      new XplainPermission(
          "VIEW_ONBOARDING_REPORT", Authorities.VIEW_ONBOARDING_REPORT, BASIC_USER, DASHBOARD);
  private static final XplainPermission RUN_ONBOARDING_REPORT =
      new XplainPermission(
          "RUN_ONBOARDING_REPORT", Authorities.RUN_ONBOARDING_REPORT, SUPER_USER, DASHBOARD);

  // VD Override
  // VD_MANUAL_OVERRIDE group was not initialized with BASIC permissions. If VIEW permission is
  // added  migration is needed to initialize BASIC role
  private static final XplainPermission RUN_VD_MANUAL_OVERRIDE =
      new XplainPermission(
          "RUN_VD_MANUAL_OVERRIDE", Authorities.RUN_VD_OVERRIDE, SUPER_USER, VD_MANUAL_OVERRIDE);

  // XM settings
  private static final XplainPermission VIEW_XM_SETTINGS =
      new XplainPermission("VIEW_XM_SETTINGS", Authorities.VIEW_XM_SETTINGS, BASIC_USER, DASHBOARD);
  private static final XplainPermission MODIFY_XM_SETTINGS =
      new XplainPermission(
          "MODIFY_XM_SETTINGS", Authorities.MODIFY_XM_SETTINGS, ADMIN_USER, DASHBOARD);

  // View Config Permissions
  private static final XplainPermission MODIFY_VIEW_CONFIGURATIONS =
      new XplainPermission(
          "MODIFY_VIEW_CONFIGURATIONS",
          Authorities.MODIFY_VIEW_CONFIGURATIONS,
          ADMIN_USER,
          VIEW_CONFIG_SETTINGS);

  @Override
  public List<XplainPermission> availablePermissions() {
    return List.of(
        VIEW_ROLE,
        MODIFY_ROLE,
        MODIFY_TEAM,
        VIEW_TEAM,
        MODIFY_FIXING,
        VIEW_FIXING,
        MODIFY_VALUATION_SETTINGS,
        VIEW_VALUATION_SETTINGS,
        MODIFY_DATA_PROVIDER,
        VIEW_DATA_PROVIDER,
        MODIFY_IDENTIFIER_SOURCE,
        VIEW_IDENTIFIER_SOURCE,
        MODIFY_CCY_EXPOSURE,
        VIEW_CCY_EXPOSURE,
        MODIFY_CUSTOM_FIELD_NAME,
        VIEW_CUSTOM_FIELD_NAME,
        VIEW_MARKET_DATA_KEY,
        MODIFY_MARKET_DATA_KEY,
        CONFIGURE_MARKET_DATA_KEY,
        MODIFY_CALENDAR,
        VIEW_CALENDAR,
        MODIFY_CONVENTION_OVERRIDE,
        VIEW_CONVENTION_OVERRIDE,
        MODIFY_TASK_DEFINITION,
        VIEW_TASK_DEFINITION,
        VIEW_MD_TASK_EXECUTION,
        RUN_MD_TASK_EXECUTION,
        VIEW_VD_TASK_DEFINITION,
        MODIFY_VD_TASK_DEFINITION,
        VIEW_VD_TASK_EXECUTION,
        RUN_VD_TASK_EXECUTION,
        VIEW_CURVE_GROUP,
        MODIFY_CURVE_GROUP,
        VIEW_CURVE,
        MODIFY_CURVE,
        VIEW_CREDIT_CURVE,
        MODIFY_CREDIT_CURVE,
        VIEW_BOND_CURVE,
        MODIFY_BOND_CURVE,
        VIEW_FX_RATE,
        MODIFY_FX_RATE,
        MODIFY_FX_VOL,
        VIEW_FX_VOL,
        VIEW_VOL_SURFACE,
        MODIFY_VOL_SURFACE,
        VIEW_CURVE_CONFIG,
        MODIFY_CURVE_CONFIG,
        RUN_CURVE_CONFIG,
        VIEW_COMPANY,
        MODIFY_COMPANY,
        VIEW_COMPANY_DOCS,
        MODIFY_COMPANY_DOCS,
        VIEW_COMPANY_VD_SETTINGS,
        MODIFY_COMPANY_VD_SETTINGS,
        VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS,
        MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS,
        VIEW_COMPANY_LEGAL_ENTITY,
        MODIFY_COMPANY_LEGAL_ENTITY,
        VIEW_COMPANY_VALUATION_SETTINGS,
        MODIFY_COMPANY_VALUATION_SETTINGS,
        VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS,
        MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS,
        VIEW_PORTFOLIO,
        MODIFY_PORTFOLIO,
        MODIFY_TRADE,
        RUN_PV_CALCULATIONS,
        VIEW_CALCULATION_RESULTS,
        RUN_CCY_EXPOSURE_SIM,
        VIEW_CCY_EXPOSURE_SIM_RESULTS,
        VIEW_TRADE,
        VIEW_MARKET_DATA_GROUP,
        MODIFY_MARKET_DATA_GROUP,
        MODIFY_MARKET_DATA,
        VIEW_MARKET_DATA,
        VIEW_MD_EXC_MANAGEMENT_RESULT,
        VIEW_VD_EXC_MANAGEMENT_RESULT,
        VIEW_MD_BREAK_TEST,
        MODIFY_MD_BREAK_TEST,
        CONFIGURE_MD_BREAK_TEST,
        VIEW_VD_BREAK_TEST,
        MODIFY_VD_BREAK_TEST,
        CONFIGURE_VD_BREAK_TEST,
        VIEW_ON_BREAK_TEST,
        MODIFY_ON_BREAK_TEST,
        CONFIGURE_ON_BREAK_TEST,
        VIEW_VALUATION_DATA_GROUP,
        MODIFY_VALUATION_DATA_GROUP,
        MODIFY_VALUATION_DATA,
        VIEW_VALUATION_DATA,
        VIEW_AUDIT_ENTRY,
        VIEW_ACCESS_LOG,
        VIEW_DASHBOARD,
        RUN_DASHBOARD,
        VIEW_ONBOARDING_REPORT,
        RUN_ONBOARDING_REPORT,
        RUN_VD_MANUAL_OVERRIDE,
        MODIFY_VIEW_CONFIGURATIONS,
        VIEW_XM_SETTINGS,
        MODIFY_XM_SETTINGS);
  }
}
