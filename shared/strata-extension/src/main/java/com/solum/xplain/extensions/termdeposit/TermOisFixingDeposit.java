package com.solum.xplain.extensions.termdeposit;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.Resolvable;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.index.OvernightIndexObservation;
import com.opengamma.strata.product.Product;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@Builder
@Getter
@NullMarked
public class TermOisFixingDeposit implements Product, Resolvable<ResolvedTermOisFixingDeposit> {

  private final BuySell buySell;
  private final Currency currency;
  private final double notional;
  private final LocalDate startDate;
  private final LocalDate endDate;
  private final OvernightTermIndex index;
  private final DaysAdjustment fixingDateOffset;
  private final DayCount dayCount;
  private final double fixedRate;

  @Override
  public ResolvedTermOisFixingDeposit resolve(ReferenceData refData) {
    LocalDate fixingDate = this.fixingDateOffset.adjust(startDate, refData);
    LocalDate effectiveDate = this.index.calculateEffectiveFromFixing(startDate, refData);
    LocalDate maturityDate = this.index.calculateMaturityFromFixing(startDate, refData);
    double yearFraction = this.dayCount.yearFraction(effectiveDate, maturityDate);
    return ResolvedTermOisFixingDeposit.builder()
        .currency(this.getCurrency())
        .notional(this.buySell.normalize(this.notional))
        .observation(
            OvernightIndexObservation.builder()
                .index(this.index)
                .fixingDate(fixingDate)
                .publicationDate(this.index.calculatePublicationFromFixing(fixingDate, refData))
                .yearFraction(yearFraction)
                .effectiveDate(effectiveDate)
                .maturityDate(maturityDate)
                .build())
        .floatingRate(
            OvernightCompoundedRateComputation.of(
                this.index, effectiveDate, maturityDate, 0, refData))
        .fixedRate(this.fixedRate)
        .build();
  }

  @Override
  public ImmutableSet<Currency> allCurrencies() {
    return ImmutableSet.of(this.currency);
  }
}
