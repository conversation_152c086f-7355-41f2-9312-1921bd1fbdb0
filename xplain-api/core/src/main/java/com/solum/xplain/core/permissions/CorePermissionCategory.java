package com.solum.xplain.core.permissions;

import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.ADMIN;
import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.CURVES;
import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.DATA;
import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.EXCEPTION_MANAGEMENT;
import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.OTHER;
import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.PORTFOLIOS;
import static com.solum.xplain.core.permissions.CorePermissionCategoryGroup.VALUATIONS;

import com.solum.xplain.core.permissions.extension.PermissionCategory;
import com.solum.xplain.core.permissions.extension.PermissionCategoryGroup;

public enum CorePermissionCategory implements PermissionCategory {
  CURVE_CONFIGURATIONS(CURVES),
  CURVE_GROUPS(CURVES),

  COMPANY_LIST(PORTFOLIOS),
  PORTFOLIO_LIST(PORTFOLIOS),
  CCY_EXPOSURE(PORTFOLIOS),

  MARKET_DATA(DATA),
  FIXINGS(DATA),
  VALUATION_DATA(DATA),
  DATA_PROVIDERS(DATA),
  CUSTOM_ATTRIBUTES(DATA),
  MARKET_DATA_KEYS(DATA),
  CALENDARS(DATA),
  CONVENTION_OVERRIDES(DATA),

  PV_CALCULATIONS(VALUATIONS),
  VALUATION_SETTINGS(VALUATIONS),
  CCY_EXPOSURE_SIMULATIONS(VALUATIONS),

  DASHBOARD(EXCEPTION_MANAGEMENT),
  MD_TASK_EXECUTION(EXCEPTION_MANAGEMENT),
  VD_TASK_EXECUTION(EXCEPTION_MANAGEMENT),
  BREAK_TESTS(EXCEPTION_MANAGEMENT),
  TASK_MANAGEMENT(EXCEPTION_MANAGEMENT),
  VD_MANUAL_OVERRIDE(EXCEPTION_MANAGEMENT),

  ROLES(ADMIN),
  TEAMS(ADMIN),

  AUDIT_ENTRIES(OTHER),
  VIEW_CONFIG_SETTINGS(OTHER);

  private final PermissionCategoryGroup taskCategory;

  CorePermissionCategory(PermissionCategoryGroup group) {
    this.taskCategory = group;
  }

  @Override
  public PermissionCategoryGroup getGroup() {
    return taskCategory;
  }
}
