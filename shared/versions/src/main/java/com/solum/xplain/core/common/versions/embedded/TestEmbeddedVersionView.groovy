package com.solum.xplain.core.common.versions.embedded


import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import org.springframework.data.mongodb.core.mapping.Document

@Document
@EqualsAndHashCode
@ToString(includeSuper = true)
class TestEmbeddedVersionView extends DateRangeVersionedEntity {

  private String value

  String getValue() {
    return value
  }

  void setValue(String value) {
    this.value = value
  }
}
