package com.solum.xplain.core.authentication;

public class Authorities {

  public static final String MODIFY_ROLE = "M_ROLE";
  public static final String VIEW_ROLE = "V_ROLE";

  public static final String MODIFY_TEAM = "M_TEAM";
  public static final String VIEW_TEAM = "V_TEAM";

  public static final String MODIFY_FIXING = "M_FIXING";
  public static final String VIEW_FIXING = "V_FIXING";

  public static final String MODIFY_VALUATION_SETTINGS = "M_VAL_S";
  public static final String VIEW_VALUATION_SETTINGS = "V_VAL_S";

  public static final String MODIFY_DATA_PROVIDER = "M_DP";
  public static final String VIEW_DATA_PROVIDER = "V_DP";
  public static final String MODIFY_IDENTIFIER_SOURCE = "M_IS";
  public static final String VIEW_IDENTIFIER_SOURCE = "V_IS";
  public static final String MODIFY_CUSTOM_FIELD_NAME = "M_CFN";
  public static final String VIEW_CUSTOM_FIELD_NAME = "V_CFN";

  public static final String MODIFY_MARKET_DATA_KEY = "M_MDK";
  public static final String VIEW_MARKET_DATA_KEY = "V_MDK";
  public static final String CONFIGURE_MARKET_DATA_KEY = "C_MDK";

  public static final String MODIFY_CALENDAR = "M_CAL";
  public static final String VIEW_CALENDAR = "V_CAL";
  public static final String MODIFY_CONVENTION_OVERRIDE = "M_CONV_O";
  public static final String VIEW_CONVENTION_OVERRIDE = "V_CONV_O";

  public static final String MODIFY_TASK_DEFINITION = "M_TASK_D";
  public static final String VIEW_TASK_DEFINITION = "V_TASK_D";

  public static final String RUN_MD_TASK_EXECUTION = "R_TASK_E";
  public static final String VIEW_MD_TASK_EXECUTION = "V_TASK_E";

  public static final String VIEW_VD_TASK_DEFINITION = "V_VD_TASK_D";
  public static final String MODIFY_VD_TASK_DEFINITION = "M_VD_TASK_D";

  public static final String VIEW_VD_TASK_EXECUTION = "V_VD_TASK_E";
  public static final String RUN_VD_TASK_EXECUTION = "R_VD_TASK_E";

  public static final String VIEW_CURVE_GROUP = "V_CG";
  public static final String MODIFY_CURVE_GROUP = "M_CG";

  public static final String VIEW_CURVE = "V_CG_C";
  public static final String MODIFY_CURVE = "M_CG_C";

  public static final String VIEW_CREDIT_CURVE = "V_CG_CC";
  public static final String MODIFY_CREDIT_CURVE = "M_CG_CC";

  public static final String VIEW_BOND_CURVE = "V_CG_BC";
  public static final String MODIFY_BOND_CURVE = "M_CG_BC";

  public static final String VIEW_FX_RATE = "V_CG_FXR";
  public static final String MODIFY_FX_RATE = "M_CG_FXR";

  public static final String MODIFY_FX_VOL = "M_CG_FX_VOL";
  public static final String VIEW_FX_VOL = "V_CG_FX_VOL";

  public static final String VIEW_VOL_SURFACE = "V_CG_VOL_SURF";
  public static final String MODIFY_VOL_SURFACE = "M_CG_VOL_SURF";

  public static final String VIEW_CURVE_CONFIG = "V_C_CONF";
  public static final String MODIFY_CURVE_CONFIG = "M_C_CONF";
  public static final String RUN_CURVE_CONFIG = "R_CG";

  public static final String VIEW_COMPANY = "V_COMP";
  public static final String MODIFY_COMPANY = "M_COMP";

  public static final String VIEW_COMPANY_VD_SETTINGS = "V_COMP_VD";
  public static final String MODIFY_COMPANY_VD_SETTINGS = "M_COMP_VD";

  public static final String VIEW_COMPANY_DOCUMENTS = "V_COMP_DOCS";
  public static final String MODIFY_COMPANY_DOCUMENTS = "M_COMP_DOCS";

  public static final String VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS = "V_COMP_LE_VD";
  public static final String MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS = "M_COMP_LE_VD";

  public static final String VIEW_COMPANY_LEGAL_ENTITY = "V_COMP_LE";
  public static final String MODIFY_COMPANY_LEGAL_ENTITY = "M_COMP_LE";

  public static final String VIEW_COMPANY_VALUATION_SETTINGS = "V_COMP_VS";
  public static final String MODIFY_COMPANY_VALUATION_SETTINGS = "M_COMP_VS";

  public static final String VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS = "V_COMP_LE_VS";
  public static final String MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS = "M_COMP_LE_VS";

  public static final String VIEW_PORTFOLIO = "V_PF";
  public static final String MODIFY_PORTFOLIO = "M_PF";
  public static final String VIEW_TRADE = "V_TRADE";
  public static final String MODIFY_TRADE = "M_TRADE";

  public static final String MODIFY_CCY_EXPOSURE = "M_CCY_EXPOSURE";
  public static final String VIEW_CCY_EXPOSURE = "V_CCY_EXPOSURE";

  public static final String RUN_CCY_EXPOSURE_SIM = "R_CCY_EXPOSURE_SIM";
  public static final String VIEW_CCY_EXPOSURE_SIM = "V_CCY_EXPOSURE_SIM";

  public static final String RUN_PV_CALCULATIONS = "R_PV_CALC";

  public static final String VIEW_CALCULATION_RESULTS = "V_CALC_R";

  public static final String VIEW_MARKET_DATA_GROUP = "V_MDG";
  public static final String MODIFY_MARKET_DATA_GROUP = "M_MDG";

  public static final String MODIFY_MARKET_DATA = "M_MDV";
  public static final String VIEW_MARKET_DATA = "V_MDV";

  public static final String VIEW_MD_EXC_MANAGEMENT_RESULT = "V_EXC_MD_MGMT_R";
  public static final String VIEW_VD_EXC_MANAGEMENT_RESULT = "V_EXC_VD_MGMT_R";

  public static final String VIEW_MD_BREAK_TEST = "V_BR_T";
  public static final String MODIFY_MD_BREAK_TEST = "M_BR_T";
  public static final String CONFIGURE_MD_BREAK_TEST = "C_BR_T";

  public static final String VIEW_VD_BREAK_TEST = "V_VD_BR_T";
  public static final String MODIFY_VD_BREAK_TEST = "M_VD_BR_T";
  public static final String CONFIGURE_VD_BREAK_TEST = "C_VD_BR_T";

  public static final String VIEW_ON_BREAK_TEST = "V_ON_BR_T";
  public static final String MODIFY_ON_BREAK_TEST = "M_ON_BR_T";
  public static final String CONFIGURE_ON_BREAK_TEST = "C_ON_BR_T";

  public static final String VIEW_VALUATION_DATA_GROUP = "V_VD_DG";
  public static final String MODIFY_VALUATION_DATA_GROUP = "M_VD_DG";

  public static final String MODIFY_VALUATION_DATA = "M_VD_DV";
  public static final String VIEW_VALUATION_DATA = "V_VD_DV";

  public static final String VIEW_AUDIT_ENTRY = "V_AUD_E";

  public static final String VIEW_ACCESS_LOG = "V_AXS_L";

  public static final String VIEW_DASHBOARD = "V_DASH";
  public static final String RUN_DASHBOARD = "R_DASH";

  public static final String VIEW_ONBOARDING_REPORT = "V_ONB";
  public static final String RUN_ONBOARDING_REPORT = "R_ONB";

  public static final String RUN_VD_OVERRIDE = "R_VDO";

  public static final String MODIFY_XM_SETTINGS = "M_XM_S";
  public static final String VIEW_XM_SETTINGS = "V_XM_S";

  public static final String MODIFY_VIEW_CONFIGURATIONS = "M_VW_CNF";

  private static final String PREFIX = "hasAuthority('";
  private static final String SUFFIX = "')";

  public static final String AUTHORITY_VIEW_ROLE = PREFIX + VIEW_ROLE + SUFFIX;
  public static final String AUTHORITY_MODIFY_ROLE = PREFIX + MODIFY_ROLE + SUFFIX;

  public static final String AUTHORITY_VIEW_TEAM = PREFIX + VIEW_TEAM + SUFFIX;
  public static final String AUTHORITY_MODIFY_TEAM = PREFIX + MODIFY_TEAM + SUFFIX;

  public static final String AUTHORITY_VIEW_FIXING = PREFIX + VIEW_FIXING + SUFFIX;
  public static final String AUTHORITY_MODIFY_FIXING = PREFIX + MODIFY_FIXING + SUFFIX;

  public static final String AUTHORITY_VIEW_VALUATION_SETTINGS =
      PREFIX + VIEW_VALUATION_SETTINGS + SUFFIX;
  public static final String AUTHORITY_MODIFY_VALUATION_SETTINGS =
      PREFIX + MODIFY_VALUATION_SETTINGS + SUFFIX;

  public static final String AUTHORITY_VIEW_DATA_PROVIDER = PREFIX + VIEW_DATA_PROVIDER + SUFFIX;
  public static final String AUTHORITY_MODIFY_DATA_PROVIDER =
      PREFIX + MODIFY_DATA_PROVIDER + SUFFIX;
  public static final String AUTHORITY_VIEW_IDENTIFIER_SOURCE =
      PREFIX + VIEW_IDENTIFIER_SOURCE + SUFFIX;
  public static final String AUTHORITY_MODIFY_IDENTIFIER_SOURCE =
      PREFIX + MODIFY_IDENTIFIER_SOURCE + SUFFIX;

  public static final String AUTHORITY_VIEW_CUSTOM_FIELD_NAME =
      PREFIX + VIEW_CUSTOM_FIELD_NAME + SUFFIX;
  public static final String AUTHORITY_MODIFY_CUSTOM_FIELD_NAME =
      PREFIX + MODIFY_CUSTOM_FIELD_NAME + SUFFIX;

  public static final String AUTHORITY_VIEW_MARKET_DATA_KEY =
      PREFIX + VIEW_MARKET_DATA_KEY + SUFFIX;
  public static final String AUTHORITY_MODIFY_MARKET_DATA_KEY =
      PREFIX + MODIFY_MARKET_DATA_KEY + SUFFIX;

  public static final String AUTHORITY_VIEW_CALENDAR = PREFIX + VIEW_CALENDAR + SUFFIX;
  public static final String AUTHORITY_MODIFY_CALENDAR = PREFIX + MODIFY_CALENDAR + SUFFIX;

  public static final String AUTHORITY_VIEW_CONVENTION_OVERRIDE =
      PREFIX + VIEW_CONVENTION_OVERRIDE + SUFFIX;
  public static final String AUTHORITY_MODIFY_CONVENTION_OVERRIDE =
      PREFIX + MODIFY_CONVENTION_OVERRIDE + SUFFIX;

  public static final String AUTHORITY_VIEW_TASK_DEFINITION =
      PREFIX + VIEW_TASK_DEFINITION + SUFFIX;
  public static final String AUTHORITY_MODIFY_TASK_DEFINITION =
      PREFIX + MODIFY_TASK_DEFINITION + SUFFIX;

  public static final String AUTHORITY_VIEW_TASK_EXECUTION =
      PREFIX + VIEW_MD_TASK_EXECUTION + SUFFIX;
  public static final String AUTHORITY_RUN_TASK_EXECUTION = PREFIX + RUN_MD_TASK_EXECUTION + SUFFIX;

  public static final String AUTHORITY_VIEW_VD_TASK_DEFINITION =
      PREFIX + VIEW_VD_TASK_DEFINITION + SUFFIX;
  public static final String AUTHORITY_MODIFY_VD_TASK_DEFINITION =
      PREFIX + MODIFY_VD_TASK_DEFINITION + SUFFIX;

  public static final String AUTHORITY_VIEW_VD_TASK_EXECUTION =
      PREFIX + VIEW_VD_TASK_EXECUTION + SUFFIX;
  public static final String AUTHORITY_RUN_VD_TASK_EXECUTION =
      PREFIX + RUN_VD_TASK_EXECUTION + SUFFIX;

  public static final String AUTHORITY_VIEW_CURVE_GROUP = PREFIX + VIEW_CURVE_GROUP + SUFFIX;
  public static final String AUTHORITY_MODIFY_CURVE_GROUP = PREFIX + MODIFY_CURVE_GROUP + SUFFIX;

  public static final String AUTHORITY_VIEW_CURVE = PREFIX + VIEW_CURVE + SUFFIX;
  public static final String AUTHORITY_MODIFY_CURVE = PREFIX + MODIFY_CURVE + SUFFIX;

  public static final String AUTHORITY_VIEW_CREDIT_CURVE = PREFIX + VIEW_CREDIT_CURVE + SUFFIX;
  public static final String AUTHORITY_MODIFY_CREDIT_CURVE = PREFIX + MODIFY_CREDIT_CURVE + SUFFIX;

  public static final String AUTHORITY_VIEW_BOND_CURVE = PREFIX + VIEW_BOND_CURVE + SUFFIX;
  public static final String AUTHORITY_MODIFY_BOND_CURVE = PREFIX + MODIFY_BOND_CURVE + SUFFIX;

  public static final String AUTHORITY_VIEW_FX_RATE = PREFIX + VIEW_FX_RATE + SUFFIX;
  public static final String AUTHORITY_MODIFY_FX_RATE = PREFIX + MODIFY_FX_RATE + SUFFIX;

  public static final String AUTHORITY_VIEW_FX_VOL = PREFIX + VIEW_FX_VOL + SUFFIX;
  public static final String AUTHORITY_MODIFY_FX_VOL = PREFIX + MODIFY_FX_VOL + SUFFIX;

  public static final String AUTHORITY_VIEW_VOL_SURFACE = PREFIX + VIEW_VOL_SURFACE + SUFFIX;
  public static final String AUTHORITY_MODIFY_VOL_SURFACE = PREFIX + MODIFY_VOL_SURFACE + SUFFIX;

  public static final String AUTHORITY_VIEW_CURVE_CONFIG = PREFIX + VIEW_CURVE_CONFIG + SUFFIX;
  public static final String AUTHORITY_MODIFY_CURVE_CONFIG = PREFIX + MODIFY_CURVE_CONFIG + SUFFIX;
  public static final String AUTHORITY_RUN_CURVE_CONFIG = PREFIX + RUN_CURVE_CONFIG + SUFFIX;

  public static final String AUTHORITY_VIEW_COMPANY = PREFIX + VIEW_COMPANY + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY = PREFIX + MODIFY_COMPANY + SUFFIX;
  public static final String AUTHORITY_VIEW_COMPANY_DOCUMENTS =
      PREFIX + VIEW_COMPANY_DOCUMENTS + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY_DOCUMENTS =
      PREFIX + MODIFY_COMPANY_DOCUMENTS + SUFFIX;
  public static final String AUTHORITY_VIEW_COMPANY_VD_SETTINGS =
      PREFIX + VIEW_COMPANY_VD_SETTINGS + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY_VD_SETTINGS =
      PREFIX + MODIFY_COMPANY_VD_SETTINGS + SUFFIX;

  public static final String AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS =
      PREFIX + VIEW_COMPANY_LEGAL_ENTITY_VD_SETTINGS + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS =
      PREFIX + MODIFY_COMPANY_LEGAL_ENTITY_VD_SETTINGS + SUFFIX;

  public static final String AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY =
      PREFIX + VIEW_COMPANY_LEGAL_ENTITY + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY_LEGAL_ENTITY =
      PREFIX + MODIFY_COMPANY_LEGAL_ENTITY + SUFFIX;

  public static final String AUTHORITY_VIEW_COMPANY_VALUATION_SETTINGS =
      PREFIX + VIEW_COMPANY_VALUATION_SETTINGS + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY_VALUATION_SETTINGS =
      PREFIX + MODIFY_COMPANY_VALUATION_SETTINGS + SUFFIX;

  public static final String AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS =
      PREFIX + VIEW_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS + SUFFIX;
  public static final String AUTHORITY_MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS =
      PREFIX + MODIFY_COMPANY_LEGAL_ENTITY_VALUATION_SETTINGS + SUFFIX;

  public static final String AUTHORITY_VIEW_PORTFOLIO = PREFIX + VIEW_PORTFOLIO + SUFFIX;
  public static final String AUTHORITY_MODIFY_PORTFOLIO = PREFIX + MODIFY_PORTFOLIO + SUFFIX;

  public static final String AUTHORITY_VIEW_TRADE = PREFIX + VIEW_TRADE + SUFFIX;
  public static final String AUTHORITY_MODIFY_TRADE = PREFIX + MODIFY_TRADE + SUFFIX;
  public static final String AUTHORITY_MODIFY_CCY_EXPOSURE = PREFIX + MODIFY_CCY_EXPOSURE + SUFFIX;
  public static final String AUTHORITY_VIEW_CCY_EXPOSURE = PREFIX + VIEW_CCY_EXPOSURE + SUFFIX;

  public static final String AUTHORITY_RUN_CCY_EXPOSURE_SIM =
      PREFIX + RUN_CCY_EXPOSURE_SIM + SUFFIX;
  public static final String AUTHORITY_VIEW_CCY_EXPOSURE_SIM =
      PREFIX + VIEW_CCY_EXPOSURE_SIM + SUFFIX;

  public static final String AUTHORITY_RUN_PV_CALCULATIONS = PREFIX + RUN_PV_CALCULATIONS + SUFFIX;

  public static final String AUTHORITY_VIEW_CALCULATION_RESULTS =
      PREFIX + VIEW_CALCULATION_RESULTS + SUFFIX;

  public static final String AUTHORITY_VIEW_MARKET_DATA_GROUP =
      PREFIX + VIEW_MARKET_DATA_GROUP + SUFFIX;
  public static final String AUTHORITY_MODIFY_MARKET_DATA_GROUP =
      PREFIX + MODIFY_MARKET_DATA_GROUP + SUFFIX;

  public static final String AUTHORITY_VIEW_MARKET_DATA = PREFIX + VIEW_MARKET_DATA + SUFFIX;
  public static final String AUTHORITY_MODIFY_MARKET_DATA = PREFIX + MODIFY_MARKET_DATA + SUFFIX;

  public static final String AUTHORITY_VIEW_MD_EXC_MANAGEMENT_RESULT =
      PREFIX + VIEW_MD_EXC_MANAGEMENT_RESULT + SUFFIX;
  public static final String AUTHORITY_VIEW_VD_EXC_MANAGEMENT_RESULT =
      PREFIX + VIEW_VD_EXC_MANAGEMENT_RESULT + SUFFIX;

  public static final String AUTHORITY_VIEW_MD_BREAK_TEST = PREFIX + VIEW_MD_BREAK_TEST + SUFFIX;
  public static final String AUTHORITY_MODIFY_MD_BREAK_TEST =
      PREFIX + MODIFY_MD_BREAK_TEST + SUFFIX;
  public static final String AUTHORITY_CONFIGURE_MD_BREAK_TEST =
      PREFIX + CONFIGURE_MD_BREAK_TEST + SUFFIX;

  public static final String AUTHORITY_VIEW_ON_BREAK_TEST = PREFIX + VIEW_ON_BREAK_TEST + SUFFIX;
  public static final String AUTHORITY_MODIFY_ON_BREAK_TEST =
      PREFIX + MODIFY_ON_BREAK_TEST + SUFFIX;
  public static final String AUTHORITY_CONFIGURE_ON_BREAK_TEST =
      PREFIX + CONFIGURE_ON_BREAK_TEST + SUFFIX;

  public static final String AUTHORITY_VIEW_VD_BREAK_TEST = PREFIX + VIEW_VD_BREAK_TEST + SUFFIX;
  public static final String AUTHORITY_MODIFY_VD_BREAK_TEST =
      PREFIX + MODIFY_VD_BREAK_TEST + SUFFIX;
  public static final String AUTHORITY_CONFIGURE_VD_BREAK_TEST =
      PREFIX + CONFIGURE_VD_BREAK_TEST + SUFFIX;

  public static final String AUTHORITY_VIEW_VALUATION_DATA_GROUP =
      PREFIX + VIEW_VALUATION_DATA_GROUP + SUFFIX;
  public static final String AUTHORITY_MODIFY_VALUATION_DATA_GROUP =
      PREFIX + MODIFY_VALUATION_DATA_GROUP + SUFFIX;

  public static final String AUTHORITY_VIEW_VALUATION_DATA = PREFIX + VIEW_VALUATION_DATA + SUFFIX;
  public static final String AUTHORITY_MODIFY_VALUATION_DATA =
      PREFIX + MODIFY_VALUATION_DATA + SUFFIX;

  public static final String AUTHORITY_VIEW_AUDIT_ENTRY = PREFIX + VIEW_AUDIT_ENTRY + SUFFIX;
  public static final String AUTHORITY_VIEW_ACCESS_LOGS = PREFIX + VIEW_ACCESS_LOG + SUFFIX;

  public static final String AUTHORITY_VIEW_DASHBOARD = PREFIX + VIEW_DASHBOARD + SUFFIX;
  public static final String AUTHORITY_RUN_DASHBOARD = PREFIX + RUN_DASHBOARD + SUFFIX;

  public static final String AUTHORITY_VIEW_ONBOARDING_REPORT =
      PREFIX + VIEW_ONBOARDING_REPORT + SUFFIX;
  public static final String AUTHORITY_RUN_ONBOARDING_REPORT =
      PREFIX + RUN_ONBOARDING_REPORT + SUFFIX;

  public static final String AUTHORITY_RUN_VD_OVERRIDE = PREFIX + RUN_VD_OVERRIDE + SUFFIX;

  public static final String AUTHORITY_VIEW_XM_SETTINGS = PREFIX + VIEW_XM_SETTINGS + SUFFIX;
  public static final String AUTHORITY_MODIFY_XM_SETTINGS = PREFIX + MODIFY_XM_SETTINGS + SUFFIX;

  public static final String AUTHORITY_MODIFY_VIEW_CONFIGURATIONS =
      PREFIX + MODIFY_VIEW_CONFIGURATIONS + SUFFIX;

  private Authorities() {}
}
