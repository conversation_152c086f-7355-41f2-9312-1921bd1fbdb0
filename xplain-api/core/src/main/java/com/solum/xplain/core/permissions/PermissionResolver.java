package com.solum.xplain.core.permissions;

import static com.solum.xplain.core.permissions.UserType.BASIC_USER;
import static com.solum.xplain.core.permissions.UserType.SUPER_USER;

import com.solum.xplain.core.permissions.extension.XplainPermission;
import com.solum.xplain.core.permissions.provider.PermissionsProvider;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PermissionResolver {

  private final Map<String, List<XplainPermission>> permissions;

  @Autowired
  public PermissionResolver(List<PermissionsProvider> providers) {
    permissions =
        providers.stream()
            .map(PermissionsProvider::availablePermissions)
            .flatMap(Collection::stream)
            .collect(Collectors.groupingBy(c -> c.getPermissionCategory().name()));
  }

  public List<XplainPermission> getBySubCategoryAndUserType(String category, UserType userType) {
    return permissions.getOrDefault(category, List.of()).stream()
        .filter(p -> isAllowedForUserType(p, userType))
        .toList();
  }

  private boolean isAllowedForUserType(XplainPermission permission, UserType userType) {
    return switch (userType) {
      case BASIC_USER -> permission.getUserType() == BASIC_USER;
      case SUPER_USER ->
          permission.getUserType() == SUPER_USER || permission.getUserType() == BASIC_USER;
      case ADMIN_USER -> true;
    };
  }
}
