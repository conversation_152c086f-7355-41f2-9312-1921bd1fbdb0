/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import static com.solum.xplain.extensions.utils.OvernightAccrualsFixingUtils.checkedFixing;
import static com.solum.xplain.extensions.utils.OvernightAccrualsFixingUtils.fixingPublishedOnValuationDate;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.index.OvernightIndexObservation;
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries;
import com.opengamma.strata.collect.tuple.ObjDoublePair;
import com.opengamma.strata.market.explain.ExplainKey;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.rate.OvernightIndexRates;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import com.solum.xplain.extensions.utils.OvernightIndexRatesConverter;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.OptionalDouble;
import lombok.RequiredArgsConstructor;

/**
 * Rate computation implementation for a rate based on a single overnight index that is compounded.
 *
 * <p>Rates that are already fixed are retrieved from the time series of the {@link RatesProvider}.
 * Rates that are in the future and not in the cut-off period are computed as unique forward rate in
 * the full future period. Rates that are in the cut-off period (already fixed or forward) are
 * compounded.
 */
@RequiredArgsConstructor
public class XplainForwardOvernightCompoundedRateComputation
    implements RateComputationFn<OvernightCompoundedRateComputation> {
  private final ReferenceData referenceData;

  // -------------------------------------------------------------------------
  @Override
  public double rate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    OvernightIndexRates rates = provider.overnightIndexRates(computation.getIndex());
    //    var isOvernightTerm = computation.getIndex() instanceof OvernightTermIndex;

    ObservationDetails details =
        new ObservationDetails(computation, startDate, endDate, rates, referenceData, false, null);
    return details.calculateRate();
  }

  public double rate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      LocalDate termFirstFixingDate) {

    OvernightIndexRates rates = provider.overnightIndexRates(computation.getIndex());

    ObservationDetails details =
        new ObservationDetails(
            computation, startDate, endDate, rates, referenceData, true, termFirstFixingDate);
    return details.calculateRate();
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {
    OvernightIndexRates rates = provider.overnightIndexRates(computation.getIndex());
    var isOvernightTerm = computation.getIndex() instanceof OvernightTermIndex;

    ObservationDetails details =
        new ObservationDetails(
            computation, startDate, endDate, rates, referenceData, isOvernightTerm, null);

    return details.calculateRateSensitivity();
  }

  public PointSensitivityBuilder rateSensitivity(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      OvernightIndexRates rates,
      ReferenceData referenceData,
      boolean isOvernightTerm,
      LocalDate termFirstFixingDate) {
    ObservationDetails details =
        new ObservationDetails(
            computation,
            startDate,
            endDate,
            rates,
            referenceData,
            isOvernightTerm,
            termFirstFixingDate);

    return details.calculateRateSensitivity();
  }

  @Override
  public double explainRate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    double rate = rate(computation, startDate, endDate, provider);
    builder.put(ExplainKey.COMBINED_RATE, rate);
    return rate;
  }

  public ReferenceData getReferenceData() {
    return referenceData;
  }

  // -------------------------------------------------------------------------
  // Internal class. Observation details stored in a separate class to clarify the construction.
  static final class ObservationDetails {

    private final OvernightCompoundedRateComputation computation;
    private final OvernightIndexRates rates;
    private final LocalDateDoubleTimeSeries indexFixingDateSeries;
    private final DayCount dayCount;
    private final int cutoffOffset;
    private final LocalDate firstFixing; // The date of the first fixing
    private final LocalDate lastFixingNonCutoff; // The last fixing not in the cutoff period.
    private final double[]
        accrualFactorCutoff; // Accrual factors for the sub-periods using the cutoff rate.
    private final boolean isOvernightTerm;
    private LocalDate
        nextFixing; // Running variable through the different methods: next fixing date to be
    // analyzed
    private final double accrualFactorTotal; // Total accrual factor
    private final HolidayCalendar
        unadjustedFixingCalendar; // fixing calendar before enforcing additional bus days
    private final LocalDate unadjustedStartDate;
    private final LocalDate termFirstFixingDate;

    private ObservationDetails(
        OvernightCompoundedRateComputation computation,
        LocalDate startDate,
        LocalDate endDate,
        OvernightIndexRates rates,
        ReferenceData referenceData,
        boolean isOvernightTerm,
        LocalDate termFirstFixingDate) {
      rates = OvernightIndexRatesConverter.overnightRates(rates, computation.getFixingCalendar());
      this.unadjustedFixingCalendar = computation.getFixingCalendar();
      this.unadjustedStartDate = startDate;
      var enforcedBusinessDays = List.of(rates.getValuationDate(), startDate, endDate);
      var overrideComputation =
          OvernightCompoundedRateComputation.of(
              computation.getIndex(),
              computation.getStartDate(),
              computation.getEndDate(),
              computation.getRateCutOffDays(),
              ValuationDateReferenceData.wrap(referenceData, enforcedBusinessDays));

      this.computation = overrideComputation;
      this.rates = rates;
      this.indexFixingDateSeries = rates.getFixings();
      this.dayCount = overrideComputation.getIndex().getDayCount();

      if (computation.getIndex() instanceof OvernightTermIndex termIndex) {
        if (termFirstFixingDate != null) {
          this.termFirstFixingDate = termFirstFixingDate;
        } else {
          this.termFirstFixingDate =
              termIndex.getFixingDateOffset().adjust(startDate, referenceData);
        }
      } else {
        this.termFirstFixingDate = null;
      }
      // Details of the cutoff period
      this.firstFixing = startDate;
      // The date after the last fixing
      // The date of the last fixing
      LocalDate lastFixing = overrideComputation.getFixingCalendar().previous(endDate);
      this.cutoffOffset = Math.max(overrideComputation.getRateCutOffDays(), 1);
      this.accrualFactorCutoff = new double[cutoffOffset - 1];
      LocalDate currentFixing = lastFixing;
      for (int i = 0; i < cutoffOffset - 1; i++) {
        currentFixing = overrideComputation.getFixingCalendar().previous(currentFixing);
        LocalDate effectiveDate = overrideComputation.calculateEffectiveFromFixing(currentFixing);
        LocalDate maturityDate = overrideComputation.calculateMaturityFromEffective(effectiveDate);
        accrualFactorCutoff[i] = dayCount.yearFraction(effectiveDate, maturityDate);
      }
      this.lastFixingNonCutoff = currentFixing;
      this.accrualFactorTotal = dayCount.yearFraction(startDate, endDate);
      this.isOvernightTerm = isOvernightTerm;
    }

    // Composition - publication strictly before valuation date: try accessing fixing time-series
    private double pastCompositionFactor() {
      double compositionFactor = 1.0d;
      LocalDate currentFixing = firstFixing;
      LocalDate currentPublication = computation.calculatePublicationFromFixing(currentFixing);
      while ((currentFixing.isBefore(lastFixingNonCutoff))
          && // fixing in the non-cutoff period
          rates.getValuationDate().isAfter(currentPublication)) { // publication before valuation
        LocalDate effectiveDate = computation.calculateEffectiveFromFixing(currentFixing);
        LocalDate maturityDate = computation.calculateMaturityFromEffective(effectiveDate);
        double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);
        double rate =
            checkedFixing(
                currentFixing,
                indexFixingDateSeries,
                computation.getIndex(),
                unadjustedStartDate,
                unadjustedFixingCalendar);

        compositionFactor *= 1.0d + accrualFactor * rate;
        currentFixing = computation.getFixingCalendar().next(currentFixing);
        currentPublication = computation.calculatePublicationFromFixing(currentFixing);
      }
      if (currentFixing.equals(lastFixingNonCutoff)
          &&
          // fixing is on the last non-cutoff date, cutoff period known
          rates.getValuationDate().isAfter(currentPublication)) { // publication before valuation
        double rate =
            checkedFixing(
                currentFixing,
                indexFixingDateSeries,
                computation.getIndex(),
                unadjustedStartDate,
                unadjustedFixingCalendar);
        LocalDate effectiveDate = computation.calculateEffectiveFromFixing(currentFixing);
        LocalDate maturityDate = computation.calculateMaturityFromEffective(effectiveDate);
        double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);
        compositionFactor *= 1.0d + accrualFactor * rate;
        for (int i = 0; i < cutoffOffset - 1; i++) {
          compositionFactor *= 1.0d + accrualFactorCutoff[i] * rate;
        }
        currentFixing = computation.getFixingCalendar().next(currentFixing);
      }
      nextFixing = currentFixing;
      return compositionFactor;
    }

    // Composition - publication on valuation date: Check if a fixing is available on current date
    private double valuationCompositionFactor() {
      LocalDate currentFixing = nextFixing;
      LocalDate currentPublication = computation.calculatePublicationFromFixing(currentFixing);

      if (rates.getValuationDate().equals(currentPublication)
          && !(currentFixing.isAfter(
              lastFixingNonCutoff))) { // If currentFixing > lastFixingNonCutoff, everything fixed

        OptionalDouble fixedRate =
            fixingPublishedOnValuationDate(
                currentFixing,
                currentPublication,
                indexFixingDateSeries,
                unadjustedStartDate,
                unadjustedFixingCalendar);
        if (fixedRate.isPresent()) {
          nextFixing = computation.getFixingCalendar().next(nextFixing);
          LocalDate effectiveDate = computation.calculateEffectiveFromFixing(currentFixing);
          LocalDate maturityDate = computation.calculateMaturityFromEffective(effectiveDate);
          double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);
          if (currentFixing.isBefore(lastFixingNonCutoff)) {
            return 1.0d + accrualFactor * fixedRate.getAsDouble();
          }
          double compositionFactor = 1.0d + accrualFactor * fixedRate.getAsDouble();
          for (int i = 0; i < cutoffOffset - 1; i++) {
            compositionFactor *= 1.0d + accrualFactorCutoff[i] * fixedRate.getAsDouble();
          }
          return compositionFactor;
        }
      }
      return 1.0d;
    }

    // Composition - forward part in non-cutoff period; past/valuation date case dealt with in
    // previous methods
    private double compositionFactorNonCutoff() {
      if (!nextFixing.isAfter(lastFixingNonCutoff)) {
        OvernightIndexObservation obs = computation.observeOn(nextFixing);
        LocalDate startDate = obs.getEffectiveDate();
        LocalDate endDate = computation.calculateMaturityFromFixing(lastFixingNonCutoff);
        double accrualFactor = dayCount.yearFraction(startDate, endDate);
        double rate = rates.periodRate(obs, endDate);
        return 1.0d + accrualFactor * rate;
      }
      return 1.0d;
    }

    // Composition - forward part in the cutoff period; past/valuation date case dealt with in
    // previous methods
    private double compositionFactorCutoff() {
      if (!nextFixing.isAfter(lastFixingNonCutoff)) {
        OvernightIndexObservation obs = computation.observeOn(lastFixingNonCutoff);
        double rate = rates.rate(obs);
        double compositionFactor = 1.0d;
        for (int i = 0; i < cutoffOffset - 1; i++) {
          compositionFactor *= 1.0d + accrualFactorCutoff[i] * rate;
        }
        return compositionFactor;
      }
      return 1.0d;
    }

    // Calculate the total rate
    // get co-pilot to write docs here
    // if there's a fixing the only use pastCompositionFactor is there's not the use
    // valuationCompositionFactor

    // we have the libor pricing in the overnight pricing
    // if the valuation date is in the first period then we use libor pricing
    // else we use the overnight pricing

    // The fixing date is the start of the period - fixing date offset
    // if the valuation date is before the fixing date then we use the overnight pricing
    // if the valuation date is after the fixing date then we use the  libor pricing
    // if the valuation date is the fixing date then we check if the fixing is available
    // if the fixing is available then we use the libor pricing else we use the overnight pricing

    double calculateRate() {
      if (!isOvernightTerm) {
        return overnightRate();
      }

      LocalDate fixingDate = termFirstFixingDate;
      LocalDate valuationDate = rates.getValuationDate();

      if (valuationDate.isBefore(fixingDate)) {
        // Forward-looking: use overnight rate
        return overnightRate();
      } else if (valuationDate.isAfter(fixingDate)) {
        // Look back to find historical fixing
        return (pastFixedCompositionFactor(fixingDate) - 1.0) / accrualFactorTotal;
      } else {
        // valuationDate.equals(fixingDate): find fixing for today
        OptionalDouble fixedRate = indexFixingDateSeries.get(fixingDate);
        if (fixedRate.isPresent()) {
          return (pastFixedCompositionFactor(fixingDate) - 1.0) / accrualFactorTotal;
        } else {
          // Fallback to forward rate
          return overnightRate();
        }
      }
    }

    private double pastFixedCompositionFactor(LocalDate fixingDate) {
      OptionalDouble fixedRate = indexFixingDateSeries.get(fixingDate);
      if (fixedRate.isEmpty()) {
        return 1.0; // No fixing available, no rate to apply
      }

      LocalDate effectiveDate = computation.calculateEffectiveFromFixing(firstFixing);
      LocalDate maturityDate = computation.observeOn(lastFixingNonCutoff).getMaturityDate();
      double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);

      return 1.0 + accrualFactor * fixedRate.getAsDouble();
    }

    private double overnightRate() {
      return (pastCompositionFactor()
                  * valuationCompositionFactor()
                  * compositionFactorNonCutoff()
                  * compositionFactorCutoff()
              - 1.0d)
          / accrualFactorTotal;
    }

    PointSensitivityBuilder calculateRateSensitivity() {
      if (!isOvernightTerm) {
        return overnightRateSensitivity();
      }

      LocalDate fixingDate = termFirstFixingDate;
      LocalDate valuationDate = rates.getValuationDate();

      // Check if this is the first period that uses the term rate
      // Only the first period (where fixing date matches the term first fixing date) should have zero sensitivity after fixing
      boolean isFirstPeriod = isFirstTermPeriod(fixingDate);

      if (valuationDate.isBefore(fixingDate)) {
        return overnightRateSensitivity();
      } else if (valuationDate.isAfter(fixingDate)) {
        // Only return zero sensitivity if this is the first period that's actually fixed by the term rate
        return isFirstPeriod ? PointSensitivityBuilder.none() : overnightRateSensitivity();
      } else {
        // valuationDate == fixingDate
        OptionalDouble fixedRate = indexFixingDateSeries.get(fixingDate);
        if (fixedRate.isPresent()) {
          return isFirstPeriod ? PointSensitivityBuilder.none() : overnightRateSensitivity();
        } else {
          return overnightRateSensitivity();
        }
      }
    }

    private boolean isFirstTermPeriod(LocalDate fixingDate) {
      // The first period is the one where the accrual period start date corresponds to the term fixing
      // This is a heuristic - you might need to adjust this logic based on your specific term structure
      return unadjustedStartDate.equals(fixingDate) ||
             Math.abs(ChronoUnit.DAYS.between(unadjustedStartDate, fixingDate)) <= 5; // Allow for business day adjustments
    }

    private PointSensitivityBuilder overnightRateSensitivity() {
      double factor =
          this.pastCompositionFactor()
              * this.valuationCompositionFactor()
              / this.accrualFactorTotal;
      ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityNonCutoff =
          this.compositionFactorAndSensitivityNonCutoff();
      ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityCutoff =
          this.compositionFactorAndSensitivityCutoff();
      PointSensitivityBuilder combinedPointSensitivity =
          compositionFactorAndSensitivityNonCutoff
              .getFirst()
              .multipliedBy(compositionFactorAndSensitivityCutoff.getSecond() * factor);
      combinedPointSensitivity =
          combinedPointSensitivity.combinedWith(
              compositionFactorAndSensitivityCutoff
                  .getFirst()
                  .multipliedBy(compositionFactorAndSensitivityNonCutoff.getSecond() * factor));
      return combinedPointSensitivity;
    }

    private ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityCutoff() {
      OvernightIndexObservation obs = this.computation.observeOn(this.lastFixingNonCutoff);
      if (this.nextFixing.isAfter(this.lastFixingNonCutoff)) {
        return ObjDoublePair.of(PointSensitivityBuilder.none(), 1.0);
      } else {
        double rate = this.rates.rate(obs);
        double compositionFactor = 1.0;
        double compositionFactorDerivative = 0.0;

        for (int i = 0; i < this.cutoffOffset - 1; ++i) {
          compositionFactor *= 1.0 + this.accrualFactorCutoff[i] * rate;
          compositionFactorDerivative +=
              this.accrualFactorCutoff[i] / (1.0 + this.accrualFactorCutoff[i] * rate);
        }

        compositionFactorDerivative *= compositionFactor;
        PointSensitivityBuilder rateSensitivity =
            this.cutoffOffset <= 1
                ? PointSensitivityBuilder.none()
                : this.rates.ratePointSensitivity(obs);
        rateSensitivity = rateSensitivity.multipliedBy(compositionFactorDerivative);
        return ObjDoublePair.of(rateSensitivity, compositionFactor);
      }
    }

    private ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityNonCutoff() {
      if (!this.nextFixing.isAfter(this.lastFixingNonCutoff)) {
        OvernightIndexObservation obs = this.computation.observeOn(this.nextFixing);
        LocalDate startDate = obs.getEffectiveDate();
        LocalDate endDate = this.computation.calculateMaturityFromFixing(this.lastFixingNonCutoff);
        double accrualFactor = this.dayCount.yearFraction(startDate, endDate);
        double rate = this.rates.periodRate(obs, endDate);
        PointSensitivityBuilder rateSensitivity =
            this.rates.periodRatePointSensitivity(obs, endDate);
        rateSensitivity = rateSensitivity.multipliedBy(accrualFactor);
        return ObjDoublePair.of(rateSensitivity, 1.0 + accrualFactor * rate);
      } else {
        return ObjDoublePair.of(PointSensitivityBuilder.none(), 1.0);
      }
    }
  }
}
