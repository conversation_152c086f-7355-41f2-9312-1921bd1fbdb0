package com.solum.xplain.core.curveconfiguration.csv.override;

import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideForm;
import com.solum.xplain.core.instrument.InstrumentType;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ConfigurationOverrideInstrumentProviderCsvForm {

  private final InstrumentType instrumentType;
  private final CurveConfigurationProviderOverrideForm provider;

  public static ConfigurationOverrideInstrumentProviderCsvForm newOf(
      InstrumentType type,
      List<String> assetNames,
      List<String> sectors,
      List<String> fxPairs,
      String primaryProvider,
      String secondaryProvider) {
    var provider =
        CurveConfigurationProviderOverrideForm.newOf(
            primaryProvider, secondaryProvider, assetNames, sectors, fxPairs);
    return new ConfigurationOverrideInstrumentProviderCsvForm(type, provider);
  }
}
