package com.solum.xplain.core.curveconfiguration;

import static com.solum.xplain.core.curveconfiguration.CurveConfigurationUtils.parseIborIndex;
import static com.solum.xplain.core.curveconfiguration.CurveConfigurationUtils.parseIndexConfig;
import static com.solum.xplain.core.curveconfiguration.CurveConfigurationUtils.parseOis;

import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.Comparator;
import java.util.Optional;

public class CurveConfigurationIndexComparator implements Comparator<CurveConvention> {

  public static final CurveConfigurationIndexComparator INSTANCE =
      new CurveConfigurationIndexComparator();
  private static final int ONE = 1;

  @Override
  public int compare(CurveConvention c1, CurveConvention c2) {
    return compareIndexConfigurations(c1, c2).orElse(c1.getName().compareTo(c2.getName()));
  }

  private Optional<Integer> compareIndexConfigurations(CurveConvention c1, CurveConvention c2) {
    return parseIndexConfig(c1)
        .map(
            idx1 ->
                parseIndexConfig(c2)
                    .flatMap(idx2 -> compareIndexConfiguration(idx1, idx2))
                    .orElse(-ONE))
        .or(() -> parseIndexConfig(c2).map(c -> ONE));
  }

  private Optional<Integer> compareIndexConfiguration(
      IndexCurveConvention index1, IndexCurveConvention index2) {
    return parseOis(index1)
        .map(ois -> -ONE)
        .or(() -> parseOis(index2).map(ois -> ONE))
        .or(() -> compareIborIndices(index1, index2));
  }

  private Optional<Integer> compareIborIndices(
      IndexCurveConvention index1, IndexCurveConvention index2) {
    return Steps.begin(parseIborIndex(index1))
        .then(() -> parseIborIndex(index2))
        .yield((idx1, idx2) -> idx1.getTenor().compareTo(idx2.getTenor()));
  }
}
