package com.solum.xplain.valuation.calculation.breakeven;

import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.product.swap.IborRateCalculation;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.RateCalculation;
import java.util.Optional;

import com.solum.xplain.extensions.index.OvernightTermRateCalculation;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class RateCalculationUtils {

  public static Optional<Double> resolveSpread(RateCalculation calculation) {
    if (calculation instanceof IborRateCalculation) {
      return ((IborRateCalculation) calculation).getSpread().map(ValueSchedule::getInitialValue);
    } else if (calculation instanceof OvernightRateCalculation) {
      return ((OvernightRateCalculation) calculation)
              .getSpread()
              .map(ValueSchedule::getInitialValue);
    } else if (calculation instanceof OvernightTermRateCalculation) {
      return ((OvernightTermRateCalculation) calculation)
              .getSpread()
              .map(ValueSchedule::getInitialValue);
    }
    return Optional.empty();
  }
}
