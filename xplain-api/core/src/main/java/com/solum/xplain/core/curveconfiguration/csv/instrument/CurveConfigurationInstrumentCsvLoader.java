package com.solum.xplain.core.curveconfiguration.csv.instrument;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.toEnum;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.providers.enums.DataProviderType.MARKET;
import static java.lang.String.format;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.curveconfiguration.csv.override.ConfigurationOverrideInstrumentCsvForm;
import com.solum.xplain.core.curvegroup.conventions.CurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceConfigurations;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.instrument.InstrumentGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.instrument.InstrumentTypeResolver;
import com.solum.xplain.core.providers.DataProviderRepository;
import com.solum.xplain.core.providers.value.DataProviderView;
import com.solum.xplain.extensions.enums.CreditSector;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Try;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(SCOPE_PROTOTYPE)
public class CurveConfigurationInstrumentCsvLoader {

  public static final String CONFIGURATION_NAME_FIELD = "Name";
  public static final String INSTRUMENT_FIELD = "Instrument Type";
  public static final String PRIMARY_PROVIDER_FIELD = "Primary Provider";
  public static final String SECONDARY_PROVIDER_FIELD = "Secondary Provider";
  public static final String OVERRIDE_CURVES_FIELD = "Curves";
  public static final String OVERRIDE_SECTORS_FIELD = "Sectors";
  public static final String OVERRIDE_FX_PAIR_FIELD = "FX Ccy Pairs";

  private static final String DELIMITER = "\\|";
  private final Map<String, InstrumentType> instrumentsLookup;
  private final List<String> configurationInstruments;
  private final Set<String> availableProviderCodes;

  public CurveConfigurationInstrumentCsvLoader(
      DataProviderRepository providerRepository, InstrumentTypeResolver instrumentTypeResolver) {
    availableProviderCodes =
        providerRepository.dataProvidersViewListForType(MARKET).stream()
            .map(DataProviderView::getExternalId)
            .collect(Collectors.toUnmodifiableSet());
    instrumentsLookup =
        instrumentTypeResolver.values().stream()
            .collect(Collectors.toMap(InstrumentType::getLabel, identity()));
    configurationInstruments =
        instrumentTypeResolver.values().stream().map(InstrumentType::getLabel).toList();
  }

  private static ConfigurationInstrumentCsvForm toInstrumentCsvForm(
      String configurationName,
      InstrumentType type,
      String primaryProvider,
      String secondaryProvider) {
    return ConfigurationInstrumentCsvForm.newOf(
        configurationName,
        type,
        filterEmptyProviderValues(primaryProvider),
        filterEmptyProviderValues(secondaryProvider));
  }

  private static String filterEmptyProviderValues(String value) {
    return StringUtils.isNotEmpty(value) ? value : null;
  }

  public Either<ErrorItem, ConfigurationInstrumentCsvForm> parseRow(CsvRow row) {
    return Steps.begin(parseConfigurationName(row))
        .then(() -> parseInstrumentType(row, configurationInstruments))
        .then(() -> parseProvider(row, PRIMARY_PROVIDER_FIELD, true))
        .then(() -> parseProvider(row, SECONDARY_PROVIDER_FIELD, false))
        .yield(CurveConfigurationInstrumentCsvLoader::toInstrumentCsvForm);
  }

  public Either<ErrorItem, ConfigurationOverrideInstrumentCsvForm> parseOverrideRow(
      CsvRow row, Set<String> validCurveConfigurationNames) {
    return Steps.begin(parseOverrideConfigurationName(row, validCurveConfigurationNames))
        .then(() -> parseInstrumentType(row, configurationInstruments))
        .then(() -> parseProvider(row, PRIMARY_PROVIDER_FIELD, true))
        .then(() -> parseProvider(row, SECONDARY_PROVIDER_FIELD, false))
        .then((name, inst, p1, p2) -> parseOverrideFields(name, inst, p1, p2, row))
        .yield((n, inst, p1, p2, form) -> form);
  }

  private Either<ErrorItem, InstrumentType> parseInstrumentType(
      CsvRow row, List<String> validInstruments) {
    return getFieldValue(row, INSTRUMENT_FIELD, v -> validateValue(v, validInstruments))
        .map(instrumentsLookup::get);
  }

  private Either<ErrorItem, String> parseProvider(CsvRow row, String fieldName, boolean required) {
    return getFieldValue(row, fieldName, required, this::validateProvider);
  }

  private String validateProvider(String provider) {
    if (StringUtils.isNotEmpty(provider)) {
      return validateValue(provider, availableProviderCodes);
    }
    return provider;
  }

  private Either<ErrorItem, String> parseConfigurationName(CsvRow row) {
    return CsvLoaderUtils.getFieldValue(row, CONFIGURATION_NAME_FIELD, String::toUpperCase);
  }

  private Either<ErrorItem, String> parseOverrideConfigurationName(
      CsvRow row, Set<String> validCurveConfigurationNames) {
    return CsvLoaderUtils.getFieldValue(
        row,
        CONFIGURATION_NAME_FIELD,
        name -> {
          var upercaseName = name.toUpperCase();
          return CsvLoaderUtils.validateValue(upercaseName, validCurveConfigurationNames);
        });
  }

  private Either<ErrorItem, ConfigurationOverrideInstrumentCsvForm> parseOverrideFields(
      String configurationName,
      InstrumentType type,
      String primaryProvider,
      String secondaryProvider,
      CsvRow row) {
    return Steps.begin(assetNames(row, type))
        .then(() -> parseCreditSectors(row, type))
        .then(() -> parseFxPairs(row, type))
        .yield(
            (assets, sectors, fx) ->
                ConfigurationOverrideInstrumentCsvForm.newOf(
                    configurationName,
                    type,
                    filterEmptyProviderValues(primaryProvider),
                    filterEmptyProviderValues(secondaryProvider),
                    assets,
                    sectors,
                    fx));
  }

  private Either<ErrorItem, List<String>> assetNames(CsvRow row, InstrumentType type) {
    return parseArray(
            row, OVERRIDE_CURVES_FIELD, c -> validateValue(c, validOverrideAssetName(c, type)))
        .flatMap(
            values ->
                validateOverrideField(
                    values,
                    type,
                    CoreInstrumentGroup.IR,
                    CoreInstrumentGroup.VOL,
                    CoreInstrumentGroup.INFLATION));
  }

  private Either<ErrorItem, List<String>> parseCreditSectors(
      CsvRow row, InstrumentType instrumentType) {
    return parseArray(row, OVERRIDE_SECTORS_FIELD, s -> toEnum(s, CreditSector.class).name())
        .flatMap(
            values ->
                validateOverrideField(values, instrumentType, CoreInstrumentGroup.ALL_CREDIT));
  }

  private Either<ErrorItem, List<String>> parseFxPairs(CsvRow row, InstrumentType instrumentType) {
    return parseArray(row, OVERRIDE_FX_PAIR_FIELD, CsvLoaderUtils::validateCurrencypair)
        .flatMap(
            values -> validateOverrideField(values, instrumentType, CoreInstrumentGroup.ALL_FX));
  }

  private Either<ErrorItem, List<String>> validateOverrideField(
      List<String> values, InstrumentType type, InstrumentGroup... permissibleGroups) {
    if (values.isEmpty() || Set.of(permissibleGroups).contains(type.getInstrumentGroup())) {
      return Either.right(values);
    }
    return Either.left(
        PARSING_ERROR.entity(format("Overrides on %s are not allowed for %s", values, type)));
  }

  private <T> Either<ErrorItem, List<T>> parseArray(
      CsvRow row, String parameterName, Function<String, T> validateFn) {
    return row
        .findValue(parameterName)
        .map(c -> c.split(DELIMITER))
        .map(Arrays::asList)
        .orElseGet(List::of)
        .stream()
        .map(c -> Checked.now(() -> validateFn.apply(c)))
        .collect(collectingAndThen(toList(), Try::sequence))
        .toEither()
        .leftMap(err -> rowParsingError(row, parameterName, err))
        .map(ImmutableList::copyOf);
  }

  private boolean validOverrideAssetName(String assetName, InstrumentType type) {
    if (type.getInstrumentGroup() == CoreInstrumentGroup.VOL) {
      return VolatilitySurfaceConfigurations.lookupByName(assetName).isPresent();
    } else {
      return CurveConfigurations.lookupByName(assetName).stream()
          .map(CurveConvention::getAllPermissibleNodeTypes)
          .flatMap(Collection::stream)
          .anyMatch(c -> StringUtils.equals(c, type.getLabel()));
    }
  }
}
