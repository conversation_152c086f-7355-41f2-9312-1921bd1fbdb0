package com.solum.xplain.core.curveconfiguration.value;

import com.solum.xplain.core.curveconfiguration.validation.UniqueCurveConfigurationName;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@UniqueCurveConfigurationName
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CurveConfigurationForm extends CurveConfigurationUpdateForm {

  @NotEmpty private String name;
}
