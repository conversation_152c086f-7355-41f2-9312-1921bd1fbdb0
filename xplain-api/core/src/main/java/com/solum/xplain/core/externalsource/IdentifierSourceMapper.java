package com.solum.xplain.core.externalsource;

import com.solum.xplain.core.externalsource.value.IdentifierSourceCreateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceUpdateForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public interface IdentifierSourceMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", ignore = true)
  IdentifierSource toEntity(IdentifierSourceCreateForm form);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "externalSourceId", ignore = true)
  IdentifierSource toEntity(
      IdentifierSourceUpdateForm form, @MappingTarget IdentifierSource entity);

  IdentifierSource copy(IdentifierSource entity);
}
