package com.solum.xplain.core.permissions;

import com.solum.xplain.core.permissions.extension.PermissionCategory;
import com.solum.xplain.core.permissions.provider.PermissionCategoryProvider;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class PermissionCategoryAggregatorImpl implements PermissionCategoryAggregator {

  private final List<PermissionCategory> categories;

  public PermissionCategoryAggregatorImpl(List<PermissionCategoryProvider> categoryProviders) {
    this.categories =
        categoryProviders.stream()
            .map(PermissionCategoryProvider::permissionCategories)
            .flatMap(Collection::stream)
            .toList();
  }

  @Override
  public List<PermissionCategory> categories() {
    return categories;
  }
}
