package com.solum.xplain.extensions.pricers

import com.solum.xplain.extensions.index.OvernightTermIndex

import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.HolidayCalendarId
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.market.explain.ExplainKey
import com.opengamma.strata.market.explain.ExplainMap
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder
import com.opengamma.strata.pricer.PricingException
import com.opengamma.strata.pricer.rate.RateComputationFn
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation
import com.solum.xplain.extensions.constants.OvernightIndexConstants
import com.solum.xplain.extensions.index.ExtendedOvernightIndices
import com.solum.xplain.extensions.index.OvernightTermRateComputation
import java.time.LocalDate
import spock.lang.Specification

class XplainForwardOvernightCompoundedRateComputationTest extends Specification {
  static START_DATE = LocalDate.parse("2021-02-08")
  static END_DATE = LocalDate.parse("2022-02-08")
  static VALUATION_DATE = LocalDate.parse("2021-02-10")

  RateComputationFn<OvernightCompoundedRateComputation> FUNCTION = new XplainForwardOvernightCompoundedRateComputation(ReferenceData.standard())
  RateComputationFn<OvernightTermRateComputation> FUNCTION_TERM = new XplainForwardOvernightTermCompoundedRateComputationFn(ReferenceData.standard())

  def "should calculate rate"() {
    setup:
    def rates = CalibrationRatesSample.gbpRatesProvider(VALUATION_DATE)
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.rate(computation, START_DATE, VALUATION_DATE, rates), closeTo(0.0004810003d, 0.0001d)
  }

  def "should calculate rate for index with publication offset = 0 days"() {
    setup:
    // missing fixing at valuation date
    def rates = CalibrationRatesSample.chfRatesProvider(VALUATION_DATE)
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.CHF_SARON,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.rate(computation, START_DATE, VALUATION_DATE.plusDays(10), rates), closeTo(0.006029301d, 0.0001d)
  }

  def "should calculate rate for CLP-TNA"() {
    setup:
    def sd = LocalDate.parse("2023-11-27")
    def ed = LocalDate.parse("2023-12-27")
    def vd = LocalDate.parse("2023-12-04")
    def rates = CalibrationRatesSample.clpRatesProvider(vd)
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndexConstants.CLP_TNA,
      sd,
      ed,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.rate(computation, sd, vd, rates), closeTo(0.090041001d, 0.0001d)
  }

  def "should calculate rate for CLP-TNA with missing fixing at valuation date"() {
    def sd = LocalDate.parse("2023-11-27")
    def ed = LocalDate.parse("2023-12-27")
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2023-11-27"), 23075.2)
      .put(LocalDate.parse("2023-11-28"), 23080.97)
      .put(LocalDate.parse("2023-11-29"), 23086.74)
      .put(LocalDate.parse("2023-11-30"), 23092.51)
      .put(LocalDate.parse("2023-12-01"), 23092.28)
      .build()

    def vd = LocalDate.parse("2023-12-04")
    def rates = CalibrationRatesSample.clpRatesProvider(vd)
      .toBuilder()
      .timeSeries([(OvernightIndexConstants.CLP_TNA): overrideTimeseries])
      .build()
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndexConstants.CLP_TNA,
      sd,
      ed,
      ReferenceData.standard())

    expect:
    that FUNCTION.rate(computation, sd, vd, rates), closeTo(0.03652902853d, 0.0000001d)
  }

  def "should throw error if CLP-TNA fixing missing at T-2"() {
    setup:
    def sd = LocalDate.parse("2023-11-27")
    def ed = LocalDate.parse("2023-12-27")
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2023-11-27"), 23075.2)
      .put(LocalDate.parse("2023-11-28"), 23080.97)
      .put(LocalDate.parse("2023-11-29"), 23086.74)
      .put(LocalDate.parse("2023-11-30"), 23092.51)
      .build()

    def vd = LocalDate.parse("2023-12-04")
    def rates = CalibrationRatesSample.clpRatesProvider(vd)
      .toBuilder()
      .timeSeries([(OvernightIndexConstants.CLP_TNA): overrideTimeseries])
      .build()
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndexConstants.CLP_TNA,
      sd,
      ed,
      ReferenceData.standard())

    when:
    FUNCTION.rate(computation, sd, vd, rates)

    then:
    PricingException error = thrown()
    error.message == "Could not get fixing value of index CLP-TNA for date 2023-12-01"
  }

  def "should calculate rate on saturday"() {
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .put(LocalDate.parse("2021-02-11"), 0.000484)
      .put(LocalDate.parse("2021-02-12"), 0.000485)
      .build()

    var valuationDate = LocalDate.parse("2021-02-13") //Saturday
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate)
      .toBuilder()
      .timeSeries([(OvernightIndices.GBP_SONIA): overrideTimeseries])
      .build()
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.rate(computation, START_DATE, valuationDate, rates), closeTo(0.0004828012d, 0.0000001d)
  }

  def "should calculate rate using fixing at T-2 when T-1 fixing isn't available"() {
    def startDate = LocalDate.parse("2022-11-24")
    def endDate = LocalDate.parse("2022-11-29")
    var valuationDate = LocalDate.parse("2022-11-29")

    def timeSeriesWithoutPreviousDayFixing = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2022-11-24"), 0.00048)
      .put(LocalDate.parse("2022-11-25"), 0.000483)
      .build()

    def timeSeriesWithPreviousDayFixing = timeSeriesWithoutPreviousDayFixing.toBuilder()
      .put(LocalDate.parse("2022-11-28"), 0.000483)
      .build()

    def ratesWithoutPrevFixing = CalibrationRatesSample.gbpRatesProvider(valuationDate)
      .toBuilder()
      .timeSeries([(OvernightIndices.GBP_SONIA): timeSeriesWithoutPreviousDayFixing])
      .build()

    def ratesWithPrevFixing = CalibrationRatesSample.gbpRatesProvider(valuationDate)
      .toBuilder()
      .timeSeries([(OvernightIndices.GBP_SONIA): timeSeriesWithPreviousDayFixing])
      .build()

    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      startDate,
      endDate,
      ReferenceData.standard()
      )

    expect:
    // should calculate equivalent rate when fixing missing since it uses fixing at "2022-11-25" with same value
    FUNCTION.rate(computation, startDate, valuationDate, ratesWithoutPrevFixing) == FUNCTION.rate(computation, startDate, valuationDate, ratesWithPrevFixing)
  }

  def "should calculate rate when start date is fixing calendar holiday"() {
    setup:
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2023-04-06"), 0.00048)
      .put(LocalDate.parse("2023-04-10"), 0.00048)
      .build()

    var valuationDate = LocalDate.parse("2023-04-11")
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate, Currency.USD, "USD-SOFR", OvernightIndices.USD_SOFR)
      .toBuilder()
      .timeSeries([(OvernightIndices.USD_SOFR): overrideTimeseries])
      .build()

    def computation =  OvernightCompoundedRateComputation.builder()
      .index(OvernightIndices.USD_SOFR)
      .fixingCalendar(HolidayCalendarId.of("USGS").resolve(ReferenceData.standard()))
      .startDate(LocalDate.parse("2023-04-07")) // fixing calendar (USGS) holiday
      .endDate(LocalDate.parse("2024-04-07"))
      .rateCutOffDays(1)
      .build()
    expect:
    that FUNCTION.rate(computation, LocalDate.parse("2023-04-07"), valuationDate, rates), closeTo(0.00048000d, 0.00000001d)
  }

  def "should calculate rate when end date is fixing calendar holiday"() {
    setup:
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2022-04-06"), 0.00048)
      .put(LocalDate.parse("2022-04-07"), 0.00048)
      .build()

    var valuationDate = LocalDate.parse("2022-04-07")
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate, Currency.USD, "USD-SOFR", OvernightIndices.USD_SOFR)
      .toBuilder()
      .timeSeries([(OvernightIndices.USD_SOFR): overrideTimeseries])
      .build()

    def computation =  OvernightCompoundedRateComputation.builder()
      .index(OvernightIndices.USD_SOFR)
      .fixingCalendar(HolidayCalendarId.of("USGS").resolve(ReferenceData.standard()))
      .startDate(LocalDate.parse("2022-04-07")) // fixing calendar (USGS) holiday
      .endDate(LocalDate.parse("2023-04-07"))
      .rateCutOffDays(1)
      .build()
    expect:
    that FUNCTION.rate(computation, LocalDate.parse("2022-04-07"), LocalDate.parse("2023-04-07"), rates), closeTo(0.00567123d, 0.00000001d)
  }

  def "should return error for rate sensitivity"() {
    when:
    def overrideTimeseries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .put(LocalDate.parse("2021-02-11"), 0.000484)
      .put(LocalDate.parse("2021-02-12"), 0.000485)
      .build()

    var valuationDate = LocalDate.parse("2021-02-13") //Saturday
    def rates = CalibrationRatesSample.gbpRatesProvider(valuationDate)
      .toBuilder()
      .timeSeries([(OvernightIndices.GBP_SONIA): overrideTimeseries])
      .build()
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    then:
    FUNCTION.rateSensitivity(computation, START_DATE, valuationDate, rates) == PointSensitivityBuilder.none()
  }

  def "should calculate rate with explain"() {
    setup:
    def rates = CalibrationRatesSample.gbpRatesProvider(VALUATION_DATE)
    def explain = ExplainMap.builder()
    def computation = OvernightCompoundedRateComputation.of(
      OvernightIndices.GBP_SONIA,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )

    expect:
    that FUNCTION.explainRate(computation, START_DATE, VALUATION_DATE, rates, explain), closeTo(0.0004810003d, 0.0001d)
    that explain.build().get(ExplainKey.COMBINED_RATE).get(), closeTo(0.0004810003d, 0.0001d)
  }

  // This is for overnight term
  def "should calculate rate for overnight term"() {
    setup:
    def rates = CalibrationRatesSample.usdTermRatesProvider(VALUATION_DATE)

    def computationOvernight = OvernightCompoundedRateComputation.of(
      ExtendedOvernightIndices.USD_SOFR_3M,
      START_DATE,
      END_DATE,
      ReferenceData.standard()
      )
    //delegate
    def computation = OvernightTermRateComputation.of(computationOvernight, LocalDate.parse("2021-02-08"))

    expect:
    that FUNCTION_TERM.rate(computation, START_DATE, VALUATION_DATE, rates), closeTo(0.0004810003d, 0.0001d)
  }
}
