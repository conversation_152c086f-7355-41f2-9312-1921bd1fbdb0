package com.solum.xplain.core.externalsource;

import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static io.atlassian.fugue.Either.right;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.externalsource.value.IdentifierSourceCreateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceUpdateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class IdentifierSourceRepository {

  private final MongoOperations mongoOperations;
  private final IdentifierSourceMapper mapper;
  private final AuditingHandler auditingHandler;
  private final ConversionService conversionService;

  public List<IdentifierSourceView> list(TableFilter tableFilter, Sort sort, boolean archived) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(where(IdentifierSource.Fields.archived).is(archived)))
            .add(
                project()
                    .and(IdentifierSource.Fields.id)
                    .as(IdentifierSourceView.Fields.id)
                    .and(IdentifierSource.Fields.name)
                    .as(IdentifierSourceView.Fields.name)
                    .and(IdentifierSource.Fields.externalSourceId)
                    .as(IdentifierSourceView.Fields.externalSourceId))
            .add(match(tableFilter.criteria(IdentifierSourceView.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    } else {
      operations.add(sort(Sort.by(IdentifierSourceView.Fields.externalSourceId)));
    }
    return mongoOperations
        .aggregate(
            newAggregation(IdentifierSource.class, operations.build()), IdentifierSourceView.class)
        .getMappedResults();
  }

  public List<String> activeIdentifierSourcesExtIds() {
    return activeIdentifierSources().map(IdentifierSourceView::getExternalSourceId).toList();
  }

  public Stream<IdentifierSourceView> activeIdentifierSources() {
    return list(TableFilter.emptyTableFilter(), unsorted(), false).stream();
  }

  public Either<ErrorItem, EntityId> insert(IdentifierSourceCreateForm form) {
    var source = mongoOperations.insert(mapper.toEntity(form));
    return right(entityId(source.getId()));
  }

  public Either<ErrorItem, EntityId> update(String id, IdentifierSourceUpdateForm form) {
    return entity(id)
        .map(
            existing -> {
              var modified = mapper.toEntity(form, mapper.copy(existing));
              var diff = existing.diff(modified);
              if (diff.numberOfDiffs() > 0) {
                modified.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
                mongoOperations.save(modified);
              }
              return entityId(modified.getId());
            });
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return entity(id)
        .map(IdentifierSource::archived)
        .map(mongoOperations::save)
        .map(IdentifierSource::getId)
        .map(EntityId::entityId);
  }

  public boolean existsByExternalId(String externalSourceId) {
    return mongoOperations.exists(
        query(
            where(IdentifierSource.Fields.externalSourceId)
                .is(externalSourceId)
                .and(IdentifierSource.Fields.archived)
                .is(false)),
        IdentifierSource.class);
  }

  private Either<ErrorItem, IdentifierSource> entity(String id) {
    return mongoOperations
        .query(IdentifierSource.class)
        .matching(
            where(IdentifierSource.Fields.id)
                .is(id)
                .andOperator(where(IdentifierSource.Fields.archived).is(false)))
        .one()
        .map(Either::<ErrorItem, IdentifierSource>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Identifier source not found")));
  }
}
