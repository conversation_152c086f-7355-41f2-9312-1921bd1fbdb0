package com.solum.xplain.core.customfield.value;

import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.customfield.validation.UniqueCustomFieldName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CustomFieldNameCreateForm extends CustomFieldNameUpdateForm {
  @Schema(description = "Immutable unique identifier for custom field name")
  @NotEmpty
  @ValidIdentifier
  @UniqueCustomFieldName
  private String externalId;
}
