package com.solum.xplain.extensions.index;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.schedule.Schedule;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.opengamma.strata.product.swap.NegativeRateMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.RateAccrualPeriod;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.SwapLegType;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Optional;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * A wrapper around OvernightRateCalculation that adds support for fixingDateOffset, similar to how
 * IborRateCalculation handles fixing date offsets. This is specifically designed for term overnight
 * rates that need custom fixing date offsets.
 */
@Getter
@EqualsAndHashCode
public class OvernightTermRateCalculation implements RateCalculation, Serializable {

  private final OvernightRateCalculation delegate;

  private final DaysAdjustment fixingDateOffset;

  /**
   * Constructor for OvernightTermRateCalculation.
   *
   * @param delegate the underlying OvernightRateCalculation
   * @param fixingDateOffset the fixing date offset
   */
  public OvernightTermRateCalculation(
      OvernightRateCalculation delegate, DaysAdjustment fixingDateOffset) {
    this.delegate = delegate;
    this.fixingDateOffset = fixingDateOffset;
  }

  /**
   * Creates an OvernightTermRateCalculation with the specified parameters.
   *
   * @param dayCount the day count convention
   * @param index the overnight index
   * @param fixingDateOffset the fixing date offset
   * @param spread the spread schedule, may be null
   * @param accrualMethod the accrual method
   * @param rateCutOffDays the rate cut-off days
   * @param negativeRateMethod the negative rate method
   * @return the overnight term rate calculation
   */
  public static OvernightTermRateCalculation of(
      DayCount dayCount,
      OvernightIndex index,
      DaysAdjustment fixingDateOffset,
      ValueSchedule spread,
      OvernightAccrualMethod accrualMethod,
      int rateCutOffDays,
      NegativeRateMethod negativeRateMethod) {

    var delegate =
        OvernightRateCalculation.builder()
            .dayCount(dayCount)
            .index(index)
            .spread(spread)
            .accrualMethod(accrualMethod)
            .rateCutOffDays(rateCutOffDays)
            .negativeRateMethod(negativeRateMethod)
            .build();

    return new OvernightTermRateCalculation(delegate, fixingDateOffset);
  }

  /**
   * Creates a builder for OvernightTermRateCalculation.
   *
   * @return the builder
   */
  public static Builder builder() {
    return new Builder();
  }

  // Delegate methods to the underlying OvernightRateCalculation

  @Override
  public SwapLegType getType() {
    return SwapLegType.OTHER;
  }

  public DayCount getDayCount() {
    return delegate.getDayCount();
  }

  @Override
  public void collectCurrencies(ImmutableSet.Builder<Currency> builder) {}

  @Override
  public void collectIndices(ImmutableSet.Builder<Index> builder) {}

  public OvernightIndex getIndex() {
    return delegate.getIndex();
  }

  public Optional<ValueSchedule> getSpread() {
    return delegate.getSpread();
  }

  public OvernightAccrualMethod getAccrualMethod() {
    return delegate.getAccrualMethod();
  }

  public int getRateCutOffDays() {
    return delegate.getRateCutOffDays();
  }

  public NegativeRateMethod getNegativeRateMethod() {
    return delegate.getNegativeRateMethod();
  }

  @Override
  public ImmutableList<RateAccrualPeriod> createAccrualPeriods(
      Schedule accrualSchedule, Schedule paymentSchedule, ReferenceData refData) {

    DoubleArray resolvedGearings = ValueSchedule.ALWAYS_1.resolveValues(accrualSchedule);
    DoubleArray resolvedSpreads =
        delegate.getSpread().orElse(ValueSchedule.ALWAYS_0).resolveValues(accrualSchedule);

    ImmutableList.Builder<RateAccrualPeriod> accrualPeriods = ImmutableList.builder();

    for (int i = 0; i < accrualSchedule.size(); i++) {
      var period = accrualSchedule.getPeriod(i);
      double yearFraction = period.yearFraction(delegate.getDayCount(), accrualSchedule);

      LocalDate fixingStartDate = fixingDateOffset.resolve(refData).adjust(period.getStartDate());

      // Create OvernightTermRateComputation with the custom fixing start date
      var rateComputation =
          OvernightTermRateComputation.of(
              delegate.getIndex(),
              period.getStartDate(),
              period.getEndDate(),
              delegate.getRateCutOffDays(),
              delegate.getAccrualMethod(),
              fixingStartDate,
              refData);

      accrualPeriods.add(
          RateAccrualPeriod.builder()
              .startDate(period.getStartDate())
              .endDate(period.getEndDate())
              .unadjustedStartDate(period.getUnadjustedStartDate())
              .unadjustedEndDate(period.getUnadjustedEndDate())
              .yearFraction(yearFraction)
              .rateComputation(rateComputation)
              .gearing(resolvedGearings.get(i))
              .spread(resolvedSpreads.get(i))
              .negativeRateMethod(delegate.getNegativeRateMethod())
              .build());
    }

    return accrualPeriods.build();
  }

  @Override
  public String toString() {
    return "OvernightTermRateCalculation{"
        + "delegate="
        + delegate
        + ", fixingDateOffset="
        + fixingDateOffset
        + '}';
  }

  /**
   * Builder for OvernightTermRateCalculation that provides a fluent API similar to the standard
   * OpenGamma builders.
   */
  public static class Builder {
    private DayCount dayCount;
    private OvernightIndex index;
    private DaysAdjustment fixingDateOffset;
    private ValueSchedule spread;
    private OvernightAccrualMethod accrualMethod = OvernightAccrualMethod.COMPOUNDED;
    private int rateCutOffDays = 0;
    private NegativeRateMethod negativeRateMethod = NegativeRateMethod.ALLOW_NEGATIVE;

    public Builder dayCount(DayCount dayCount) {
      this.dayCount = dayCount;
      return this;
    }

    public Builder index(OvernightIndex index) {
      this.index = index;
      return this;
    }

    public Builder fixingDateOffset(DaysAdjustment fixingDateOffset) {
      this.fixingDateOffset = fixingDateOffset;
      return this;
    }

    public Builder spread(ValueSchedule spread) {
      this.spread = spread;
      return this;
    }

    public Builder accrualMethod(OvernightAccrualMethod accrualMethod) {
      this.accrualMethod = accrualMethod;
      return this;
    }

    public Builder rateCutOffDays(int rateCutOffDays) {
      this.rateCutOffDays = rateCutOffDays;
      return this;
    }

    public Builder negativeRateMethod(NegativeRateMethod negativeRateMethod) {
      this.negativeRateMethod = negativeRateMethod;
      return this;
    }

    public OvernightTermRateCalculation build() {
      return of(
          dayCount,
          index,
          fixingDateOffset,
          spread,
          accrualMethod,
          rateCutOffDays,
          negativeRateMethod);
    }
  }
}
