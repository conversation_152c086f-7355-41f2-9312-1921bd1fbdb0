package com.solum.xplain.core.curveconfiguration;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.curveconfiguration.validation.OnshoreOffshoreIborFixingDepositOverridesValidator.validateIborFixingDepositOverride;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationNameView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationOverrideForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.curveconfiguration.value.CurveGroupCurves;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CurveConfigurationControllerService {

  private final CurveConfigurationRepository repository;
  private final CurveGroupCurveRepository curveGroupCurveRepository;
  private final CurveConfigurationValuationsResolver valuationsResolver;

  @Transactional
  public Either<ErrorItem, EntityId> create(CurveConfigurationForm form) {
    Optional<ErrorItem> validationError = validateIborFixingDepositOverrides(form);
    return validationError
        .<Either<ErrorItem, EntityId>>map(Either::left)
        .orElseGet(() -> repository.insert(form));
  }

  public Either<ErrorItem, CurveConfigurationView> getOne(String entityId, BitemporalDate vd) {
    return repository.getView(entityId, vd);
  }

  public List<CurveConfigurationView> getAll(
      BitemporalDate stateDate, VersionedEntityFilter filter, Sort sort) {
    return repository.curveConfigurationViews(stateDate, filter, sort).stream().toList();
  }

  public List<CurveConfigurationView> getAllForMD(
      BitemporalDate stateDate, String marketDataGroupId) {
    var mdCurveConfigs =
        ofNullable(valuationsResolver.curveConfigsIdsByMarketData(stateDate).get(marketDataGroupId))
            .orElse(Collections.emptySet());
    return repository
        .curveConfigurationViews(stateDate, VersionedEntityFilter.active(), Sort.unsorted())
        .stream()
        .filter(c -> mdCurveConfigs.contains(c.getEntityId()))
        .toList();
  }

  public List<CurveConfigurationView> versions(String entityId) {
    return repository.versions(entityId);
  }

  public DateList futureVersions(CurveConfigurationSearchForm form) {
    return repository.futureVersions(form);
  }

  public List<CurveConfigurationNameView> getAllForCurveGroup(String groupId, LocalDate stateDate) {
    return repository.curveConfigForCurveGroupViews(stateDate, groupId);
  }

  public Either<ErrorItem, CurveGroupCurves> getCurveGroupCurves(
      String entityId, BitemporalDate vd) {
    return repository
        .getView(entityId, vd)
        .map(
            v ->
                CurveGroupCurves.of(
                    v.getCurveGroupId(),
                    curveGroupCurveRepository.getCurveViews(v.getCurveGroupId(), vd, active())));
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      String entityId, LocalDate versionDate, CurveConfigurationUpdateForm f) {
    Optional<ErrorItem> validationError = validateIborFixingDepositOverrides(f);
    return validationError
        .<Either<ErrorItem, EntityId>>map(Either::left)
        .orElseGet(() -> repository.update(entityId, versionDate, f));
  }

  /**
   * @param form form to validate
   * @return error when override contains an offshore curve without the onshore equivalent (or
   *     vice-versa) for IborFixingDeposit instruments. This is due to the fact that
   *     Offshore/Onshore IBOR curves share an IborFixingDeposit node, and the provider mapping must
   *     be the same for both curves.
   */
  private Optional<ErrorItem> validateIborFixingDepositOverrides(
      CurveConfigurationUpdateForm form) {
    if (form != null && form.getOverrides() != null && !form.getOverrides().isEmpty()) {
      List<CurveConfigurationOverrideForm> overrides = form.getOverrides();
      for (CurveConfigurationOverrideForm override : overrides) {
        List<String> iborFixingDepositOverridesCurveNames =
            override.getInstruments().get(CoreInstrumentType.IBOR_FIXING_DEPOSIT).getAssetNames();
        if (!iborFixingDepositOverridesCurveNames.isEmpty()) {
          for (String curveName : iborFixingDepositOverridesCurveNames) {
            Optional<ErrorItem> errorItem =
                validateIborFixingDepositOverride(curveName, iborFixingDepositOverridesCurveNames);
            if (errorItem.isPresent()) {
              return errorItem;
            }
          }
        }
      }
    }
    return Optional.empty();
  }

  @Transactional
  public Either<ErrorItem, EntityId> delete(String entityId, LocalDate versionDate) {
    return repository.delete(entityId, versionDate);
  }

  @Transactional
  public Either<ErrorItem, EntityId> archive(
      String entityId, LocalDate versionDate, ArchiveEntityForm form) {
    return repository.archive(entityId, versionDate, form);
  }
}
