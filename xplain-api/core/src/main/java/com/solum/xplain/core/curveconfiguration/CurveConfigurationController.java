package com.solum.xplain.core.curveconfiguration;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_CURVE_CONFIG;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CURVE_CONFIG;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationNameView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import com.solum.xplain.core.curveconfiguration.value.CurveGroupCurves;
import com.solum.xplain.core.lock.RequireLock;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/curve-configuration")
public class CurveConfigurationController {

  private final CurveConfigurationControllerService service;
  private final CurveConfigurationImportService importService;
  private final CurveConfigurationExportService exportService;

  public CurveConfigurationController(
      CurveConfigurationControllerService service,
      CurveConfigurationImportService importService,
      CurveConfigurationExportService exportService) {
    this.service = service;
    this.importService = importService;
    this.exportService = exportService;
  }

  @Operation(summary = "Create new curve configuration")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> newCurveConfiguration(
      @Valid @RequestBody CurveConfigurationForm form) {
    return eitherErrorItemResponse(service.create(form));
  }

  @Operation(summary = "Get curves configurations")
  @GetMapping
  @Sorted
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public List<CurveConfigurationView> getCurveConfigurations(
      @SortDefault(sort = CurveConfigurationView.Fields.name) Sort sort,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) boolean withArchived) {
    var bitemporalStateDate = BitemporalDate.newOf(stateDate);
    return service.getAll(bitemporalStateDate, VersionedEntityFilter.of(withArchived), sort);
  }

  @Operation(summary = "Get active curves configurations for market data group")
  @GetMapping("/market-data/{marketDataGroupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public List<CurveConfigurationView> getCurveConfigurationsForMD(
      @PathVariable String marketDataGroupId, @RequestParam LocalDate stateDate) {
    var bitemporalStateDate = BitemporalDate.newOf(stateDate);
    return service.getAllForMD(bitemporalStateDate, marketDataGroupId);
  }

  @Operation(summary = "Get curves configurations names for curve group")
  @GetMapping("/curve-group/{curveGroupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public List<CurveConfigurationNameView> getCurveConfigurationsForCurveGroup(
      @RequestParam LocalDate stateDate, @PathVariable String curveGroupId) {
    return service.getAllForCurveGroup(curveGroupId, stateDate);
  }

  @Operation(summary = "Get one curve configuration")
  @GetMapping("/{configId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public ResponseEntity<CurveConfigurationView> getCurveConfiguration(
      @PathVariable("configId") String entityId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(service.getOne(entityId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Get curve configuration curve group curves")
  @GetMapping("/{entityId}/curves")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public ResponseEntity<CurveGroupCurves> getCurveConfigurationCurveGroupCurves(
      @PathVariable String entityId, @RequestParam LocalDate stateDate) {
    var bitemporalDate = BitemporalDate.newOf(stateDate);
    return eitherErrorItemResponse(service.getCurveGroupCurves(entityId, bitemporalDate));
  }

  @Operation(summary = "Get curve configuration future versions")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public DateList getVersions(@Valid CurveConfigurationSearchForm search) {
    return service.futureVersions(search);
  }

  @Operation(summary = "Get curve configuration versions")
  @GetMapping("/{entityId}/versions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public List<CurveConfigurationView> getVersions(@PathVariable String entityId) {
    return service.versions(entityId);
  }

  @Operation(summary = "Update curve configuration")
  @PutMapping("/{entityId}/{versionDate}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> updateCurveConfiguration(
      @PathVariable String entityId,
      @PathVariable LocalDate versionDate,
      @Valid @RequestBody CurveConfigurationUpdateForm form) {
    return eitherErrorItemResponse(service.update(entityId, versionDate, form));
  }

  @Operation(summary = "Archive curve configuration")
  @PutMapping("/{entityId}/{versionDate}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> updateArchived(
      @PathVariable String entityId,
      @PathVariable LocalDate versionDate,
      @Valid @RequestBody ArchiveEntityForm f) {
    return eitherErrorItemResponse(service.archive(entityId, versionDate, f));
  }

  @Operation(summary = "Delete curve configuration")
  @PutMapping("/{entityId}/{versionDate}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> deleteCurveConfiguration(
      @PathVariable String entityId, @PathVariable LocalDate versionDate) {
    return eitherErrorItemResponse(service.delete(entityId, versionDate));
  }

  @Operation(summary = "Upload curve configurations CSV file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadConfigurations(
      @Valid ImportOptions importOptions, @RequestPart MultipartFile file) throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadConfigurations(importOptions, file.getBytes()));
  }

  @Operation(summary = "Upload curve configuration overrides for a specific config CSV file")
  @PostMapping(
      value = "/{entityId}/{version}/overrides/csv",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadConfigurationOverrides(
      @PathVariable("entityId") String entityId,
      @PathVariable("version") LocalDate version,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadOverridesForConfig(entityId, version, importOptions, file.getBytes()));
  }

  @Operation(summary = "Upload curve configuration overrides all configs CSV file")
  @PostMapping(value = "/upload-overrides", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_CONFIG)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadConfigurationOverrides(
      @Valid ImportOptions importOptions, @RequestPart MultipartFile file) throws IOException {
    return eitherErrorItemsResponse(importService.uploadOverrides(importOptions, file.getBytes()));
  }

  @Operation(summary = "Get one curve configuration in csv format")
  @GetMapping("/{configId}/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public ResponseEntity<ByteArrayResource> getConfigurationCsv(
      @PathVariable("configId") String entityId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.getOneConfigurationCsv(entityId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Get all curve configurations in csv format")
  @GetMapping("/csv")
  @Sorted
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public ResponseEntity<ByteArrayResource> getConfigurationCsv(
      @SortDefault(sort = CurveConfigurationView.Fields.name) Sort sort,
      @RequestParam LocalDate stateDate) {
    var bitemporalStateDate = BitemporalDate.newOf(stateDate);
    return exportService.getAllConfigurationsCsv(bitemporalStateDate, sort).toResponse();
  }

  @Operation(summary = "Get curve configuration overrides in csv format")
  @GetMapping("/{configId}/overrides/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_CONFIG)
  public ResponseEntity<ByteArrayResource> getConfigurationOverridesCsv(
      @PathVariable("configId") String entityId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.getConfigurationOverridesCsv(entityId, BitemporalDate.newOf(stateDate)));
  }
}
