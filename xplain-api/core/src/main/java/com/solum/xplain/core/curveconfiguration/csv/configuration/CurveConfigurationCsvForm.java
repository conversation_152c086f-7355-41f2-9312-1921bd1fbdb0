package com.solum.xplain.core.curveconfiguration.csv.configuration;

import com.solum.xplain.core.curveconfiguration.csv.instrument.ConfigurationInstrumentCsvForm;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(staticName = "newOf")
public class CurveConfigurationCsvForm {
  private final String curveGroupId;
  private final ConfigurationInstrumentCsvForm instrumentCsvForm;

  public String configurationName() {
    return instrumentCsvForm.getConfigurationName();
  }
}
