package com.solum.xplain.extensions.index;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.date.TenorAdjustment;
import com.opengamma.strata.basics.index.FloatingRateName;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.collect.named.ExtendedEnum;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@NullMarked
@EqualsAndHashCode
@Getter
public class OvernightTermIndex implements OvernightIndex, Serializable {

  private final String name;
  private final Currency currency;
  private final boolean active;
  private final HolidayCalendarId fixingCalendar;
  private final DaysAdjustment fixingDateOffset;
  private final DaysAdjustment effectiveDateOffset;
  private final TenorAdjustment maturityDateOffset;
  private final DayCount dayCount;
  private final Tenor tenor;
  private final String floatingRateName;

  public OvernightTermIndex(
      String name,
      Currency currency,
      boolean active,
      HolidayCalendarId fixingCalendar,
      DaysAdjustment fixingDateOffset,
      DaysAdjustment effectiveDateOffset,
      TenorAdjustment maturityDateOffset,
      DayCount dayCount,
      Tenor tenor) {
    this.name = name;
    this.currency = currency;
    this.active = active;
    this.fixingCalendar = fixingCalendar;
    this.fixingDateOffset = fixingDateOffset;
    this.dayCount = dayCount;
    this.tenor = tenor;
    this.effectiveDateOffset = effectiveDateOffset;
    this.maturityDateOffset = maturityDateOffset;
    String suffix = "-" + tenor;
    if (!name.endsWith(suffix)) {
      throw new IllegalArgumentException(
          Messages.format(
              "OvernightTermIndex name '{}' must end with tenor '{}'", name, tenor.toString()));
    } else {
      this.floatingRateName = name.substring(0, name.length() - suffix.length());
    }
  }

  public static OvernightTermIndex of(String name) {
    return extendedEnum().lookup(name);
  }

  public static ExtendedEnum<OvernightTermIndex> extendedEnum() {
    return ExtendedEnum.of(OvernightTermIndex.class);
  }

  public int getPublicationDateOffset() {
    return 0;
  }

  public int getEffectiveDateOffset() {
    return this.effectiveDateOffset.getDays();
  }

  public LocalDate calculatePublicationFromFixing(LocalDate fixingDate, ReferenceData refData) {
    HolidayCalendar fixingCal = this.fixingCalendar.resolve(refData);
    return fixingCal.shift(fixingCal.nextOrSame(fixingDate), getPublicationDateOffset());
  }

  @Override
  public LocalDate calculateEffectiveFromFixing(LocalDate fixingDate, ReferenceData refData) {
    LocalDate fixingBusinessDay = fixingCalendar.resolve(refData).nextOrSame(fixingDate);
    return effectiveDateOffset.adjust(fixingBusinessDay, refData);
  }

  @Override
  public LocalDate calculateMaturityFromFixing(LocalDate fixingDate, ReferenceData refData) {
    LocalDate fixingBusinessDay = fixingCalendar.resolve(refData).nextOrSame(fixingDate);
    return maturityDateOffset.adjust(fixingDateOffset.adjust(fixingBusinessDay, refData), refData);
  }

  @Override
  public LocalDate calculateFixingFromEffective(LocalDate effectiveDate, ReferenceData refData) {
    LocalDate effectiveBusinessDay = effectiveDateCalendar(refData).nextOrSame(effectiveDate);
    return fixingDateOffset.adjust(effectiveBusinessDay, refData);
  }

  @Override
  public LocalDate calculateMaturityFromEffective(LocalDate effectiveDate, ReferenceData refData) {
    LocalDate effectiveBusinessDay = effectiveDateCalendar(refData).nextOrSame(effectiveDate);
    return maturityDateOffset.adjust(effectiveBusinessDay, refData);
  }

  @Override
  public boolean isActive() {
    return this.active;
  }

  @Override
  public FloatingRateName getFloatingRateName() {
    return FloatingRateName.of(this.floatingRateName);
  }

  private HolidayCalendar effectiveDateCalendar(ReferenceData refData) {
    HolidayCalendarId cal = effectiveDateOffset.getResultCalendar();
    if (cal == HolidayCalendarIds.NO_HOLIDAYS) {
      cal = fixingCalendar;
    }
    return cal.resolve(refData);
  }
}
