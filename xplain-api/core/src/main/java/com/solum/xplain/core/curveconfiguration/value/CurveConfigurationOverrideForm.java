package com.solum.xplain.core.curveconfiguration.value;

import com.solum.xplain.core.curveconfiguration.validation.ValidCurveConfigInstrumentSize;
import com.solum.xplain.core.curveconfiguration.validation.ValidProviderOverrideFields;
import com.solum.xplain.core.instrument.InstrumentType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Map;
import lombok.Data;

@Data
public class CurveConfigurationOverrideForm {

  @NotNull private Integer priority;

  @NotNull private Boolean enabled;

  @Valid @NotEmpty @ValidProviderOverrideFields @ValidCurveConfigInstrumentSize
  private Map<InstrumentType, CurveConfigurationProviderOverrideForm> instruments;
}
