package com.solum.xplain.core.curveconfiguration;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.curveconfiguration.csv.configuration.CurveConfigurationCsvImportService;
import com.solum.xplain.core.curveconfiguration.csv.override.CurveConfigurationOverrideCsvImportService;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveConfigurationImportService {
  private final CurveConfigurationCsvImportService configurationCsvImportService;
  private final CurveConfigurationOverrideCsvImportService overrideCsvImportService;

  public CurveConfigurationImportService(
      CurveConfigurationCsvImportService configurationCsvImportService,
      CurveConfigurationOverrideCsvImportService overrideCsvImportService) {
    this.configurationCsvImportService = configurationCsvImportService;
    this.overrideCsvImportService = overrideCsvImportService;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadConfigurations(
      ImportOptions importOptions, byte[] bytes) {
    return configurationCsvImportService.importCurveConfigurations(importOptions, bytes);
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadOverrides(
      ImportOptions importOptions, byte[] bytes) {
    return overrideCsvImportService.importOverrides(importOptions, bytes);
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadOverridesForConfig(
      String configId, LocalDate versionDate, ImportOptions importOptions, byte[] bytes) {
    return overrideCsvImportService.importOverridesForConfig(
        configId, versionDate, importOptions, bytes);
  }
}
