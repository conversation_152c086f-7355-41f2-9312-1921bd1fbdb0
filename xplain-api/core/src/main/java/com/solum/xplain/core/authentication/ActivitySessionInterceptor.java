package com.solum.xplain.core.authentication;

import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.MINUTES;

import com.solum.xplain.core.config.properties.OAuth2CustomProperties;
import com.solum.xplain.shared.datagrid.DataGrid;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
@Profile("!mockMvc & !test")
public class ActivitySessionInterceptor implements HandlerInterceptor {
  public static final String ACTIVITY_SESSION = "ACTIVITY_SESSION";
  private final DataGrid dataGrid;
  private final AuthenticationContext authenticationContext;
  private final int inactivityTimeout;
  private Clock clock = Clock.systemDefaultZone();

  public ActivitySessionInterceptor(
      DataGrid dataGrid,
      AuthenticationContext authenticationContext,
      OAuth2CustomProperties oAuth2CustomProperties) {
    this.dataGrid = dataGrid;
    this.authenticationContext = authenticationContext;
    this.inactivityTimeout = oAuth2CustomProperties.getInactivityTimeoutMinutes();
  }

  /** For unit testing. */
  void configureClock(Clock clock) {
    this.clock = clock;
  }

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws IOException {
    var isAnonymousClient =
        SecurityContextHolder.getContext().getAuthentication()
            instanceof AnonymousAuthenticationToken;
    if (isAnonymousClient
        || inactivityTimeout <= 0
        || authenticationContext.currentUser().isTrustedPrincipal()) {
      return true;
    }

    if ("text/event-stream".equals(request.getHeader("Accept"))) {
      return true;
    }

    var existingSessionId = existingSessionCookie(request);
    if (existingSessionId != null && isSessionActive(existingSessionId)) {
      storeSession(existingSessionId);
      return true;
    } else if (request.getRequestURI().endsWith("/me")) {
      createSession(response);
      return true;
    } else {
      createSession(response);
      response.setStatus(HttpStatus.UNAUTHORIZED.value());
      response.getWriter().write("New session requires re-authentication");
      return false;
    }
  }

  private String existingSessionCookie(HttpServletRequest request) {
    Cookie[] cookies = request.getCookies();
    if (cookies != null) {
      for (Cookie cookie : cookies) {
        if (ACTIVITY_SESSION.equals(cookie.getName())) {
          return cookie.getValue();
        }
      }
    }
    return null;
  }

  private boolean isSessionActive(String sessionId) {
    var lastSeen = (Instant) dataGrid.getKeyValueCache(ACTIVITY_SESSION).get(sessionId);
    return lastSeen != null && MINUTES.between(lastSeen, now(clock)) < inactivityTimeout;
  }

  private void storeSession(String sessionId) {
    dataGrid
        .getKeyValueCache(ACTIVITY_SESSION)
        .set(sessionId, now(clock), Duration.ofMinutes(inactivityTimeout));
  }

  private void createSession(HttpServletResponse response) {
    var newSessionId = ObjectId.get().toHexString();
    var newSessionCookie = new Cookie(ACTIVITY_SESSION, newSessionId);
    newSessionCookie.setMaxAge(-1);
    newSessionCookie.setHttpOnly(true);
    newSessionCookie.setSecure(true);
    newSessionCookie.setPath("/");
    response.addCookie(newSessionCookie);
    storeSession(newSessionId);
  }
}
