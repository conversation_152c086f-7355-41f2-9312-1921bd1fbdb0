package com.solum.xplain.generic.type.controller.value;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SECTOR_CLASSIFIER_NAME;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.generic.type.controller.validation.NonFxTradeFormGroupProvider;
import com.solum.xplain.generic.type.controller.validation.groups.CreditTradeGroup;
import com.solum.xplain.generic.type.controller.validation.groups.FxTradeGroup;
import com.solum.xplain.generic.type.controller.validation.groups.NonCreditTradeGroup;
import com.solum.xplain.generic.type.product.details.ResolvableGenericSingleCcyTradeDetails;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@GroupSequenceProvider(NonFxTradeFormGroupProvider.class)
public class NonFxCustomTradeForm extends CustomTradeForm implements ParsableToTradeValue {

  @NotNull(groups = BespokeTradeGroup.class)
  @Null(groups = ReferenceTradeGroup.class)
  @Positive
  @Schema(description = "Notional value of the trade")
  private Double notionalValue;

  @NotEmpty(groups = CreditTradeGroup.class)
  @Null(groups = NonCreditTradeGroup.class)
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = SECTOR_CLASSIFIER_NAME)
  @Schema(description = "Credit sector of the trade. Only required for CREDIT trades")
  private String sector;

  @Null(groups = NonCreditTradeGroup.class)
  @Schema(description = "Protection of the trade. Only required for CREDIT trades")
  private String protection;

  @Null(groups = {FxTradeGroup.class, CreditTradeGroup.class})
  @Schema(description = "Pay Leg Type of the trade. Only required for RATES trades")
  private String payLegType;

  @Null(groups = {FxTradeGroup.class, CreditTradeGroup.class})
  @Schema(description = "Receive Leg Type of the trade. Only required for RATES trades")
  private String receiveLegType;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    var tradeDetails =
        ResolvableGenericSingleCcyTradeDetails.builder()
            .startDate(getStartDate())
            .endDate(getEndDate())
            .assetType(getAssetType())
            .subAssetType(getSubAssetType())
            .additionalInfo(getAdditionalInfo())
            .underlying(getUnderlying())
            .optionPosition(getOptionPosition())
            .notional(notionalValue)
            .sector(sector)
            .protection(protection)
            .payLegType(payLegType)
            .receiveLegType(receiveLegType)
            .build()
            .toTradeDetails(toTradeInfo(null));
    return Either.right(defaultTradeValue(getProductType(), tradeDetails));
  }
}
