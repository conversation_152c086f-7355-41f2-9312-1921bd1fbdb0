package com.solum.xplain.core.curveconfiguration.entity;

import com.solum.xplain.core.instrument.InstrumentType;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveConfigurationOverride implements Serializable {

  private Integer priority;
  private Map<InstrumentType, CurveConfigurationProviderOverride> instruments;
  private Boolean enabled;
}
