package com.solum.xplain.core.authentication.value;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.Data;

@Data
public class CachedPrincipal implements Serializable {

  @Serial private static final long serialVersionUID = -6589367390530647097L;
  private final XplainPrincipal principal;
  private final boolean revoked;
  private final Instant lastSeen = Instant.now();

  public CachedPrincipal revoke() {
    return new CachedPrincipal(principal, true);
  }
}
