package com.solum.xplain.core.externalsource;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_IDENTIFIER_SOURCE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_IDENTIFIER_SOURCE;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.externalsource.value.IdentifierSourceCreateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceUpdateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/identifier-sources")
@RequiredArgsConstructor
public class IdentifierSourceController {

  private final IdentifierSourceControllerService service;

  @Operation(summary = "Get all identifier sources")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_IDENTIFIER_SOURCE)
  public List<IdentifierSourceView> list(
      @RequestParam boolean archived, TableFilter tableFilter, Sort sort) {
    return service.list(tableFilter, sort, archived);
  }

  @Operation(summary = "Create data identifier source")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_IDENTIFIER_SOURCE)
  public ResponseEntity<EntityId> create(@Valid @RequestBody IdentifierSourceCreateForm form) {
    return eitherErrorItemResponse(service.insert(form));
  }

  @Operation(summary = "Update data identifier source")
  @CommonErrors
  @PutMapping("/{id}")
  @PreAuthorize(AUTHORITY_MODIFY_IDENTIFIER_SOURCE)
  public ResponseEntity<EntityId> update(
      @PathVariable String id, @Valid @RequestBody IdentifierSourceUpdateForm form) {
    return eitherErrorItemResponse(service.update(id, form));
  }

  @Operation(summary = "Archive identifier source")
  @CommonErrors
  @PutMapping("/{id}/archive")
  @PreAuthorize(AUTHORITY_MODIFY_IDENTIFIER_SOURCE)
  public ResponseEntity<EntityId> archive(@PathVariable String id) {
    return eitherErrorItemResponse(service.archive(id));
  }
}
