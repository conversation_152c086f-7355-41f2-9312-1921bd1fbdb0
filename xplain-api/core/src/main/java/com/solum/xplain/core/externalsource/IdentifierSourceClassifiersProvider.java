package com.solum.xplain.core.externalsource;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import com.solum.xplain.core.externalsource.value.IdentifierSourceView;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IdentifierSourceClassifiersProvider implements ClassifiersProvider {

  private final IdentifierSourceRepository identifierSourceRepository;

  @Override
  public List<Classifier> classifiers() {
    return List.of(
        new Classifier(
            "identifierSource",
            "Identifier Source",
            identifierSourceRepository
                .activeIdentifierSources()
                .map(this::convertToClassifier)
                .toList(),
            IdentifierSourceView.class));
  }

  private Classifier convertToClassifier(IdentifierSourceView identifierSourceView) {
    return new Classifier(
        identifierSourceView.getExternalSourceId(), identifierSourceView.getName());
  }

  @Override
  public int sortOrder() {
    return 9;
  }
}
