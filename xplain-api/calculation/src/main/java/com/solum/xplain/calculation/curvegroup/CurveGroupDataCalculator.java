package com.solum.xplain.calculation.curvegroup;

import static com.solum.xplain.calibration.credit.CreditCurvesCalibration.permisibleNamesFilter;
import static java.util.stream.Collectors.toMap;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.InterpolatedNodalCurve;
import com.opengamma.strata.pricer.curve.CalibrationMeasures;
import com.opengamma.strata.pricer.fxopt.BlackFxOptionVolatilities;
import com.solum.xplain.calculation.discounting.DiscountingCalibrationUtils;
import com.solum.xplain.calculation.discounting.ResolvedDiscountingCalibrationBundles;
import com.solum.xplain.calibration.bond.BondCurvesCalibration;
import com.solum.xplain.calibration.capfloor.CapletFloorletVolatilityCalibrationResults;
import com.solum.xplain.calibration.credit.CreditCurvesCalibration;
import com.solum.xplain.calibration.credit.CreditsCalibrationResult;
import com.solum.xplain.calibration.rates.CalibrationCombinedResultRates;
import com.solum.xplain.calibration.rates.CurvesCalibration;
import com.solum.xplain.calibration.rates.CurvesCalibrationOptions;
import com.solum.xplain.calibration.rates.group.DiscountingGroup;
import com.solum.xplain.calibration.value.CalculationShifts;
import com.solum.xplain.calibration.volatility.FxVolatilitiesCalibration;
import com.solum.xplain.calibration.volatility.VolatilitiesCalibration;
import com.solum.xplain.calibration.volatility.VolatilitiesCalibrationData;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.entity.InflationSeasonalitySettings;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import lombok.Builder;

/**
 * Calculator for curve group calibrations. Calibration discounting groups, mapped to their
 * corresponding calibration discount subgroups, are stored in a {@link
 * ResolvedDiscountingCalibrationBundles}. Since no OG calibration/bootstrapping is required for the
 * {@link VolatilitySurface}s and {@link CurveGroupFxVolatility} surfaces, these are stored outside
 * of the {@link ResolvedDiscountingCalibrationBundles}.
 */
@Builder
public class CurveGroupDataCalculator {

  private final CurveGroup curveGroup;
  private final CurveConfigurationInstrumentResolver instrumentResolver;
  private final Currency triangulationCcy;

  private final MarketData marketData;
  private final boolean allowDropMissingMdNodes;
  private final ReferenceData referenceData;
  private final ValidNodesFilter nodesFilter;
  private final CalculationShifts shifts;
  private final CurveGroupFxVolatilityMapper curveGroupFxVolatilityMapper;

  private final List<Curve> curves;
  private final List<BondCurve> bondCurves;
  private final List<CreditCurve> creditCurves;
  private final List<VolatilitySurface> surfaces;
  private final CurveGroupFxVolatility fxVolatility;

  private final CalibrationMeasures calibrationMeasures;
  private final InflationSeasonalitySettings inflationSeasonalitySettings;
  private final boolean forceIsdaInterpolators;

  private final ResolvedDiscountingCalibrationBundles calibrationBundles;

  private final boolean requiresVols;
  private final Set<CurrencyPair> requiredFxVolPairs;
  private final boolean requiresBonds;
  private final boolean requiresRates;
  private final boolean requireCredits;
  private final UnaryOperator<ErrorItem> logError;

  public CalculationCurveGroupData calculate() {
    var rates = calibrateRates();

    var ratesResult = rates.getRates();
    return CalculationCurveGroupData.builder()
        .curveGroup(curveGroup)
        .curveConfig(instrumentResolver)
        .marketData(marketData)
        .rates(convertToKeyMap(ratesResult, CalibrationCombinedResultRates::getRates))
        .bondCurves(calibrateBondCurves())
        .creditRates(convertToKeyMap(calculateCreditRates(ratesResult)))
        .caplets(convertToKeyMap(calculateCaplets(ratesResult)))
        .volatilities(calibrateVols())
        .fxOptionVols(calibrateFxVols())
        .calculationResultChartData(rates.getCalculatedCurves())
        .sensitivitiesSupported(rates.isSensitivitiesSupported())
        .curves(curves)
        .volatilitySurfaces(surfaces)
        .fxVolatility(fxVolatility)
        .creditCurves(creditCurves)
        .discountingGroups(calibrationBundles.discountings())
        .build();
  }

  private CurveGroupDiscountRatesResult calibrateRates() {
    if (requiresRates) {
      var calibrationResults =
          calibrationBundles.calibrate(curvesCalibration(), allowDropMissingMdNodes);
      return CurveGroupDiscountRatesResult.newOf(calibrationResults);
    }
    return CurveGroupDiscountRatesResult.newOf(Map.of());
  }

  private Map<RateIndex, VolatilitiesCalibrationData> calibrateVols() {
    if (requiresVols) {
      var mapResults = volsCalibration().resolveVolatilityData();
      return DiscountingCalibrationUtils.filterAndLogErrors(
          mapResults, l -> l.forEach(logError::apply));
    }
    return Map.of();
  }

  private Map<CurrencyPair, BlackFxOptionVolatilities> calibrateFxVols() {
    if (!requiredFxVolPairs.isEmpty()) {
      var mapResults = fxVolsCalibration().calibrate(requiredFxVolPairs);
      return DiscountingCalibrationUtils.filterAndLogErrors(
          mapResults, l -> l.forEach(logError::apply));
    }
    return Map.of();
  }

  private Map<String, InterpolatedNodalCurve> calibrateBondCurves() {
    if (requiresBonds) {
      var calibration =
          BondCurvesCalibration.newOf(
              bondCurves, marketData, nodesFilter, referenceData, logConsumer());
      return calibration.calibrate().getResults();
    }
    return Map.of();
  }

  private Map<DiscountingGroup, CalculationCreditCalibrationResult> calculateCreditRates(
      Map<DiscountingGroup, CalibrationCombinedResultRates> rates) {
    if (!requireCredits) {
      return Map.of();
    }
    var calibration = creditsCalibration();
    var builder = ImmutableMap.<DiscountingGroup, CalculationCreditCalibrationResult>builder();
    for (var entry : rates.entrySet()) {
      calibration
          .calibrate(entry.getValue(), permisibleNamesFilter(entry.getKey().getCreditNames()))
          .flatMap(this::toCalculationCreditResult)
          .ifPresent(r -> builder.put(entry.getKey(), r));
    }
    return builder.build();
  }

  private Optional<CalculationCreditCalibrationResult> toCalculationCreditResult(
      CreditsCalibrationResult calibrationResult) {
    var creditRatesProvider =
        calibrationResult.getCreditRatesProvider().toImmutableCreditRatesProvider(logError);
    var shiftedResults =
        calibrationResult.getShiftedCreditRatesProviders().stream()
            .flatMap(
                result ->
                    result
                        .getCreditRatesDataProvider()
                        .toImmutableCreditRatesProvider((a -> a))
                        .map(
                            rates ->
                                new CalculationShiftedFxCreditCalibrationResult(
                                    result.getCurrencyPair(), rates))
                        .stream())
            .toList();

    return creditRatesProvider.map(
        provider -> new CalculationCreditCalibrationResult(provider, shiftedResults));
  }

  private Map<DiscountingGroup, CapletFloorletVolatilityCalibrationResults> calculateCaplets(
      Map<DiscountingGroup, CalibrationCombinedResultRates> rates) {
    var calibration = volsCalibration();
    return rates.keySet().stream()
        .filter(DiscountingGroup::requireCaplets)
        .collect(
            toMap(
                Function.identity(),
                c -> calibration.calibrateCaplets(rates.get(c).getRates(), c.getCapletIndices())));
  }

  private CurvesCalibration curvesCalibration() {
    var curvesCalibrationOptions =
        CurvesCalibrationOptions.builder()
            .nodesFilter(nodesFilter)
            .calibrationMeasures(calibrationMeasures)
            .inflationSeasonalitySettings(inflationSeasonalitySettings)
            .warningsConsumer(logConsumer())
            .marketData(marketData)
            .referenceData(referenceData)
            .build();
    return new CurvesCalibration(curvesCalibrationOptions);
  }

  private CreditCurvesCalibration creditsCalibration() {
    return CreditCurvesCalibration.newOf(
        creditCurves,
        marketData,
        nodesFilter,
        referenceData,
        logConsumer(),
        forceIsdaInterpolators);
  }

  private Consumer<List<ErrorItem>> logConsumer() {
    return l -> l.forEach(logError::apply);
  }

  private VolatilitiesCalibration volsCalibration() {
    return VolatilitiesCalibration.builder()
        .curveGroupId(curveGroup.getId())
        .marketData(marketData)
        .validNodesFilter(nodesFilter)
        .volatilitySurfaces(surfaces)
        .logConsumer(logError)
        .build();
  }

  private FxVolatilitiesCalibration fxVolsCalibration() {
    return FxVolatilitiesCalibration.newOf(
        fxVolatility, marketData, nodesFilter, curveGroupFxVolatilityMapper);
  }

  private <A> Map<String, A> convertToKeyMap(Map<DiscountingGroup, A> results) {
    return results.entrySet().stream().collect(toMap(c -> c.getKey().key(), Map.Entry::getValue));
  }

  private <A, B> Map<String, B> convertToKeyMap(
      Map<DiscountingGroup, A> results, Function<A, B> mapperFn) {
    return results.entrySet().stream()
        .collect(toMap(c -> c.getKey().key(), v -> mapperFn.apply(v.getValue())));
  }
}
