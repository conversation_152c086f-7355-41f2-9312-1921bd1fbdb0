package com.solum.xplain.core.curveconfiguration;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveConfigurationUtils {
  public static Optional<IndexCurveConvention> parseIndexConfig(CurveConvention configuration) {
    return Optional.of(configuration)
        .filter(IndexCurveConvention.class::isInstance)
        .map(IndexCurveConvention.class::cast);
  }

  public static Optional<IborIndex> parseIborIndex(IndexCurveConvention configuration) {
    return Optional.of(configuration)
        .map(IndexCurveConvention::getIndex)
        .filter(IborIndex.class::isInstance)
        .map(IborIndex.class::cast);
  }

  public static Optional<OvernightIndex> parseOis(IndexCurveConvention configuration) {
    return Optional.of(configuration)
        .map(IndexCurveConvention::getIndex)
        .filter(OvernightIndex.class::isInstance)
        .map(OvernightIndex.class::cast);
  }
}
