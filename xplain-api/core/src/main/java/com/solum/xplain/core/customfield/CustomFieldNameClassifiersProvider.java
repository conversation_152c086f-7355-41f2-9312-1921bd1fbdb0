package com.solum.xplain.core.customfield;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersProvider;
import com.solum.xplain.core.customfield.value.CustomFieldNameView;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CustomFieldNameClassifiersProvider implements ClassifiersProvider {

  private final CustomFieldNameRepository customFieldNameRepository;

  @Override
  public List<Classifier> classifiers() {
    return List.of(
        new Classifier(
            "customField",
            "Custom Field",
            customFieldNameRepository.activeFieldNames().map(this::convertToClassifier).toList(),
            CustomFieldNameView.class));
  }

  private Classifier convertToClassifier(CustomFieldNameView customFieldNameView) {
    return new Classifier(customFieldNameView.getExternalId(), customFieldNameView.getName());
  }

  @Override
  public int sortOrder() {
    return 9;
  }
}
