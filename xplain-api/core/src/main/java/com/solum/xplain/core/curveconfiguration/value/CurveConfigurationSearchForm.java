package com.solum.xplain.core.curveconfiguration.value;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
public class CurveConfigurationSearchForm {

  @NotEmpty private final String name;

  @NotNull private final LocalDate stateDate;
}
