package com.solum.xplain.secmaster.trademerge

import static com.solum.xplain.core.common.versions.embedded.convert.DefaultEmbeddedVersionEntityToViewConverter.MAX_DATE
import static com.solum.xplain.core.common.versions.embedded.convert.DefaultEmbeddedVersionEntityToViewConverter.MAX_DATE_TIME
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.allocationTrade
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.irsPortfolioItem

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity
import com.solum.xplain.core.portfolio.ClientMetrics
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemEntity
import com.solum.xplain.core.portfolio.PortfolioItemToViewConverter
import com.solum.xplain.core.portfolio.PortfolioMapper
import com.solum.xplain.core.portfolio.PortfolioMapperImpl
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails
import com.solum.xplain.core.portfolio.trade.CustomTradeField
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.portfolio.trade.TradeMapperImpl
import com.solum.xplain.core.portfolio.trade.TradeValue
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.teams.TeamMapperImpl
import com.solum.xplain.extensions.enums.PositionType
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.context.annotation.AnnotationConfigApplicationContext
import spock.lang.Specification
import spock.lang.Unroll

class TradeMergeToViewConverterTest extends Specification {

  def portfolioItemEntity = new PortfolioItemEntity(
  id: "entityId",
  externalTradeId: "externalId",
  portfolioId: ObjectId.get().toString(),
  )

  def tradeValidityMergeResolver = Mock(TradeValidityMergeResolver)
  def mapperContext = new AnnotationConfigApplicationContext(PortfolioMapperImpl, TradeMapperImpl, TeamMapperImpl)
  def portfolioMapper = mapperContext.getBean(PortfolioMapper)
  Map<String, PortfolioCondensedView> portfolioMap = Mock()
  def portfolioItemConverter = new PortfolioItemToViewConverter(portfolioMap, portfolioMapper)
  def detailsMerger = Mock(TradeDetailsMerger)

  TradeMergeToViewConverter converter

  def setup() {
    converter = new TradeMergeToViewConverter(tradeValidityMergeResolver, portfolioItemConverter, detailsMerger)
  }

  def "should correctly generate views when not allocated trade"() {
    setup:
    def portfolioItem = irsPortfolioItem()
    1 * tradeValidityMergeResolver.resolveMerge(portfolioItemEntity) >> [new TradeValidityMergeResult(portfolioItem)]

    when:
    def result = converter.generateViews(portfolioItemEntity)

    then:
    result == [portfolioItem]
  }

  def "should correctly generate views when allocated trade"() {
    setup:
    def tradeDetails = Mock(TradeDetails)
    def portfolioItem = allocationTrade()
    portfolioItem.setExternalIdentifiers([new ExternalIdentifier("externalIdentifier", "externalSourceId")])
    portfolioItem.setCustomFields([new CustomTradeField("S1", "V1")])
    portfolioItem.setAllocationTradeDetails(new AllocationTradeDetails(
      referenceTradeId: "1",
      allocationNotional: 10,
      counterPartyType: CounterpartyType.BILATERAL,
      counterParty: "CP",
      clientMetrics: new ClientMetrics(99)))
    def secMaster = secMaster("1", IRS, irs(10.0), 100.0)

    portfolioMap.get(_) >> new PortfolioCondensedView(externalEntityId: "_", externalCompanyId: "_", externalPortfolioId: "_")

    1 * tradeValidityMergeResolver.resolveMerge(portfolioItemEntity) >> [new TradeValidityMergeResult(portfolioItem, secMaster, [])]
    1 * detailsMerger.mergeDetails(_, _, _) >> { ProductType a, TradeDetails b, AllocationTradeDetails c -> tradeDetails }

    when:
    def result = converter.generateViews(portfolioItemEntity)

    then:
    result.size() == 1
    result[0].getTradeDetails() == tradeDetails
    result[0].getProductType() == secMaster.getProductType()
    result[0].getAllocationTradeDetails() == portfolioItem.getAllocationTradeDetails() != null
    result[0].getOnboardingDetails() == portfolioItem.getOnboardingDetails() != null
    result[0].getExternalIdentifiers() == portfolioItem.getExternalIdentifiers() != null
    result[0].getCustomFields() == portfolioItem.getCustomFields() != null
  }

  @Unroll
  def "should correctly merge with client pv"(alocNotional, alocClientPv, smNotional, smClientPv, expectedClientPv) {
    setup:
    def portfolioItem = allocationTrade()
    portfolioItem.setAllocationTradeDetails(new AllocationTradeDetails(
      referenceTradeId: "1",
      allocationNotional: alocNotional,
      counterPartyType: CounterpartyType.CLEARED,
      counterParty: "CP",
      clientMetrics: new ClientMetrics(alocClientPv)))
    def secMaster = secMaster("1", IRS, irs(smNotional), smClientPv)

    portfolioMap.get(_) >> new PortfolioCondensedView(externalEntityId: "_", externalCompanyId: "_", externalPortfolioId: "_")
    1 * tradeValidityMergeResolver.resolveMerge(portfolioItemEntity) >> [new TradeValidityMergeResult(portfolioItem, secMaster, [])]

    def result = converter.generateViews(portfolioItemEntity)

    expect:
    result.size() == 1
    result[0].getClientMetrics() == expectedClientPv

    where:
    alocNotional | alocClientPv | smNotional | smClientPv | expectedClientPv
    10           | 999          | 100        | 50         | new ClientMetrics(999)
    10           | null         | 100        | 50         | new ClientMetrics(5)
    0            | null         | 100        | 50         | new ClientMetrics(0)
    10           | null         | 100        | 0          | new ClientMetrics(0)
    10           | null         | 0          | 50         | new ClientMetrics()
    10           | null         | 100        | null       | new ClientMetrics()
  }


  @Unroll
  def "should correctly merge with description"(String allocDescription, String smDescription, String expectedDescription) {
    setup:
    def portfolioItem = allocationTrade()
    portfolioItem.setAllocationTradeDetails(new AllocationTradeDetails(referenceTradeId: "1", allocationNotional: 1, description: allocDescription))
    def secMaster = secMasterWithDescription(smDescription)

    portfolioMap.get(_) >> new PortfolioCondensedView(externalEntityId: "_", externalCompanyId: "_", externalPortfolioId: "_")
    1 * tradeValidityMergeResolver.resolveMerge(portfolioItemEntity) >> [new TradeValidityMergeResult(portfolioItem, secMaster, [])]

    def result = converter.generateViews(portfolioItemEntity)

    expect:
    result.size() == 1
    result[0].getAllocationTradeDetails().getDescription() == allocDescription
    result[0].getDescription() == expectedDescription

    where:
    allocDescription | smDescription | expectedDescription
    "alloc comments" | "sm comments" | "alloc comments"
    "sm comments"    | "sm comments" | "sm comments"
    "alloc comments" | null          | "alloc comments"
    null             | "sm comments" | "sm comments"
  }

  def static secMaster(String id, CoreProductType productType, TradeDetails tradeDetails, Double clientPv) {
    new SecMasterTradeReadEntity(
      externalTradeId: id,
      validFrom: VAL_DT,
      recordFrom: VAL_DT.atStartOfDay(),
      productType: productType,
      tradeDetails: tradeDetails,
      clientMetrics: new ClientMetrics(clientPv),
      validities: [new DateRangeVersionValidity(MAX_DATE, VAL_DT.atStartOfDay(), MAX_DATE_TIME)])
  }

  def static secMasterWithDescription(String description) {
    new SecMasterTradeReadEntity(
      description: description,
      externalTradeId: "1",
      validFrom: VAL_DT,
      recordFrom: VAL_DT.atStartOfDay(),
      productType: IRS,
      tradeDetails: irs(LocalDate.of(2022, 10, 10)),
      clientMetrics: new ClientMetrics(100),
      validities: [new DateRangeVersionValidity(MAX_DATE, VAL_DT.atStartOfDay(), MAX_DATE_TIME)])
  }

  def static irs(double notional, cpty = "CPTY", cptyType = CounterpartyType.BILATERAL) {
    new TradeDetails(
      info: new TradeInfoDetails(
      counterParty: cpty,
      counterPartyType: cptyType
      ),
      payLeg: new TradeLegDetails(
      payReceive: PayReceive.PAY,
      type: CalculationType.FIXED,
      notional: notional
      ),
      receiveLeg: new TradeLegDetails(
      payReceive: PayReceive.RECEIVE,
      type: CalculationType.IBOR,
      notional: notional
      )
      )
  }

  def static irs(LocalDate tradeDate) {
    new TradeDetails(
      info: new TradeInfoDetails(
      counterParty: "CPTY",
      counterPartyType: CounterpartyType.CLEARED,
      tradeDate: tradeDate
      ),
      payLeg: new TradeLegDetails(
      payReceive: PayReceive.PAY,
      type: CalculationType.FIXED,
      notional: 100
      ),
      receiveLeg: new TradeLegDetails(
      payReceive: PayReceive.RECEIVE,
      type: CalculationType.IBOR,
      notional: 100
      )
      )
  }

  def static irs(String description) {
    new TradeValue(
      description: description,
      )
  }

  def static cds(double notional) {
    new TradeDetails(
      info: new TradeInfoDetails(),
      positionType: PositionType.BUY,
      payLeg: new TradeLegDetails(
      notional: notional,
      currency: "USD",
      initialValue: 1,
      dayCount: "DC",
      paymentFrequency: "1M"
      ),
      receiveLeg: new TradeLegDetails(
      notional: notional,
      currency: "USD"
      )
      )
  }
}
