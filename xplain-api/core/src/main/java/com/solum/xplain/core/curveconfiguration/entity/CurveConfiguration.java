package com.solum.xplain.core.curveconfiguration.entity;

import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.instrument.InstrumentType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = CurveConfiguration.CURVE_CONFIGURATION_COLLECTION)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class CurveConfiguration extends VersionedNamedEntity {
  public static final String CURVE_CONFIGURATION_COLLECTION = "curveConfiguration";
  private String curveGroupId;
  private Map<InstrumentType, MarketDataProviders> instruments;
  private List<CurveConfigurationOverride> overrides;

  public static CurveConfiguration newOf() {
    CurveConfiguration c = new CurveConfiguration();
    c.setEntityId(ObjectId.get().toString());
    c.setState(State.ACTIVE);
    c.setRecordDate(LocalDateTime.now());
    return c;
  }

  @Override
  public boolean valueEquals(Object entity) {
    if (entity instanceof CurveConfiguration) {
      return valueEquals((CurveConfiguration) entity);
    }
    return false;
  }

  private boolean valueEquals(CurveConfiguration curveConfiguration) {
    return super.valueEquals(curveConfiguration)
        && nullSafeIsEqualCollection(overrides, curveConfiguration.overrides)
        && Objects.equals(instruments, curveConfiguration.getInstruments())
        && StringUtils.equals(curveGroupId, curveConfiguration.curveGroupId);
  }
}
