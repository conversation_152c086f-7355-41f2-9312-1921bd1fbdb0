package com.solum.xplain.core.curveconfiguration.validation;

import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidCurveConfigurationValidator
    implements ConstraintValidator<ValidCurveConfigurationId, String> {

  private final CurveConfigurationRepository curveConfigurationRepository;

  public ValidCurveConfigurationValidator(
      CurveConfigurationRepository curveConfigurationRepository) {
    this.curveConfigurationRepository = curveConfigurationRepository;
  }

  @Override
  public boolean isValid(String configId, ConstraintValidatorContext context) {
    if (configId != null) {
      return curveConfigurationRepository.validCurveConfigurationId(configId);
    }
    return true;
  }
}
