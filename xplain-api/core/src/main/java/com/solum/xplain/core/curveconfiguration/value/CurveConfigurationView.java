package com.solum.xplain.core.curveconfiguration.value;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.instrument.InstrumentType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveConfigurationView {

  private String entityId;

  private LocalDate validFrom;
  private LocalDateTime recordDate;

  private String modifiedBy;
  private LocalDateTime modifiedAt;

  private String name;
  private String comment;
  private String curveGroupId;
  private String curveGroupName;
  private State state;
  private Map<InstrumentType, CurveConfigurationProviderView> instruments;
  private List<CurveConfigurationOverrideView> overrides;
}
