package com.solum.xplain.core.externalsource.value;

import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.externalsource.validation.ValidAndUniqueIdentifierSource;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ValidAndUniqueIdentifierSource
public class IdentifierSourceCreateForm extends IdentifierSourceUpdateForm {
  @Schema(description = "Immutable unique identifier for source of external identifier")
  @NotEmpty
  @ValidIdentifier
  private String externalSourceId;
}
