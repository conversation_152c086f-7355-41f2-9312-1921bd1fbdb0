package com.solum.xplain.core.authentication;

import static org.springframework.http.ResponseEntity.notFound;
import static org.springframework.http.ResponseEntity.ok;

import com.solum.xplain.core.users.UserMapper;
import com.solum.xplain.core.users.value.CurrentUserView;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/** Created by da<PERSON><PERSON> on 06/06/17. */
@RestController
@AllArgsConstructor
public class MeController {

  private final AuthenticationContext authenticationContext;
  private final UserMapper userMapper;
  private final AuthenticationCacheService authenticationCacheService;

  @GetMapping("/me")
  @NoRoleAuthorization
  public ResponseEntity<CurrentUserView> get(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(userMapper::fromUser)
        .toOptional()
        .map(ResponseEntity::ok)
        .orElse(notFound().build());
  }

  @PostMapping("/me/logout")
  @NoRoleAuthorization
  public ResponseEntity<String> logout(Authentication auth) {
    authenticationContext
        .userEither(auth)
        .forEach(authenticationCacheService::revokeCachedPrincipal);
    return ok().body("logout successful");
  }
}
