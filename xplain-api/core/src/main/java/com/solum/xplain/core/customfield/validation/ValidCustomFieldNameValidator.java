package com.solum.xplain.core.customfield.validation;

import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.solum.xplain.core.customfield.CustomFieldNameRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ValidCustomFieldNameValidator
    implements ConstraintValidator<ValidCustomFieldName, String> {

  private final CustomFieldNameRepository repository;

  @Override
  public boolean isValid(String externalId, ConstraintValidatorContext context) {
    return isEmpty(externalId) || repository.existsByExternalId(externalId);
  }
}
