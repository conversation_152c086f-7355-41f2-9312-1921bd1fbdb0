package com.solum.xplain.core.authentication;

import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.MINUTES;
import static org.springframework.security.oauth2.core.OAuth2ErrorCodes.INVALID_TOKEN;

import com.solum.xplain.core.authentication.value.CachedPrincipal;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.config.properties.OAuth2CustomProperties;
import java.time.Instant;
import java.util.function.Function;
import org.springframework.security.core.token.Sha512DigestUtils;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenIntrospectionClaimNames;
import org.springframework.stereotype.Component;

@Component
public class CacheableAuthenticationProxy {

  private final AuthenticationCacheService authenticationCacheService;
  private final int inactivityTimeout;

  public CacheableAuthenticationProxy(
      AuthenticationCacheService authenticationCacheService,
      OAuth2CustomProperties oAuth2CustomProperties) {
    this.authenticationCacheService = authenticationCacheService;
    this.inactivityTimeout = oAuth2CustomProperties.getInactivityTimeoutMinutes();
  }

  public <T> XplainPrincipal fetchCachedOrNewPrincipal(
      T token, Function<T, XplainPrincipal> principalRetrievalFunction) {
    XplainPrincipal principal;
    var hashedToken = Sha512DigestUtils.shaHex(token.toString());
    var cachedPrincipal = authenticationCacheService.fetchCachedPrincipal(hashedToken);
    if (cachedPrincipal.isEmpty()) {
      principal = principalRetrievalFunction.apply(token);
    } else {
      principal = cachedPrincipal.get().getPrincipal();
      if (cachedPrincipal.get().isRevoked()) {
        throw new OAuth2AuthenticationException(
            new OAuth2Error(INVALID_TOKEN, "Provided token has been revoked", null));
      }
      if (MINUTES.between(cachedPrincipal.get().getLastSeen(), now()) >= inactivityTimeout) {
        throw new OAuth2AuthenticationException(
            new OAuth2Error(INVALID_TOKEN, "Provided token has expired due to inactivity", null));
      }
      var exp = principal.getAttribute(OAuth2TokenIntrospectionClaimNames.EXP);
      if (exp instanceof Instant && ((Instant) exp).isBefore(now())) {
        throw new OAuth2AuthenticationException(
            new OAuth2Error(INVALID_TOKEN, "Provided token has expired", null));
      }
    }
    authenticationCacheService.cachePrincipal(hashedToken, new CachedPrincipal(principal, false));
    return principal;
  }
}
