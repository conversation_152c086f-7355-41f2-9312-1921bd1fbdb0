package com.solum.xplain.core.curveconfiguration.value;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CurveConfigurationProviderOverrideView extends CurveConfigurationProviderView {

  private List<String> assetNames;
  private List<String> sectors;
  private List<String> fxPairs;
}
