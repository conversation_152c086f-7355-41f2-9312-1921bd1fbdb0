package com.solum.xplain.valuation.metrics

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.market.amount.CashFlow.ofPresentValue
import static com.solum.xplain.valuation.calculation.MarketDataSample.ogMarketData
import static com.solum.xplain.valuation.metrics.CashFlowValues.payValues
import static com.solum.xplain.valuation.metrics.CashFlowValues.receiveValues

import com.opengamma.strata.basics.currency.CurrencyAmount
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.opengamma.strata.market.amount.CashFlows
import java.time.LocalDate
import spock.lang.Specification

class CashFlowValuesTest extends Specification {

  def "should compact values"() {
    setup:
    def payValues = payValues(
      CashFlows.of(ofPresentValue(LocalDate.parse("2018-01-01"),
      CurrencyAmount.of(EUR, 10),
      1)),
      EUR,
      MarketDataFxRateProvider.of(ogMarketData()))
    def receiveValues = receiveValues(
      CashFlows.of(ofPresentValue(LocalDate.parse("2018-01-01"),
      CurrencyAmount.of(EUR, -11),
      1)),
      EUR,
      MarketDataFxRateProvider.of(ogMarketData()))
    when:
    def result = payValues.combinedWith(receiveValues).compact()

    then:
    result.values == [
      new CashFlowValue(
      paymentDate: LocalDate.parse("2018-01-01"),
      currency: "EUR",
      presentPay: 10,
      presentReceive: -11,
      presentValue: -1,
      forecastPay: 10,
      forecastReceive: -11,
      forecastValue: -1,
      discountFactor: 1,
      presentValueCalculationCcy: -1,
      forecastValueCalculationCcy: -1)
    ]
  }
}
