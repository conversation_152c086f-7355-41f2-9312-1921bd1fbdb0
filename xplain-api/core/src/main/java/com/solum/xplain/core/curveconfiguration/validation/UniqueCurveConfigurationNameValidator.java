package com.solum.xplain.core.curveconfiguration.validation;

import static com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository.uniqueEntityCriteria;
import static org.apache.commons.lang3.ObjectUtils.anyNull;

import com.solum.xplain.core.common.validation.UniqueEntitySupport;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import org.springframework.stereotype.Component;

@Component
public class UniqueCurveConfigurationNameValidator
    implements ConstraintValidator<UniqueCurveConfigurationName, CurveConfigurationForm> {

  private final UniqueEntitySupport uniqueSupport;

  public UniqueCurveConfigurationNameValidator(UniqueEntitySupport uniqueNameSupport) {
    this.uniqueSupport = uniqueNameSupport;
  }

  @Override
  public boolean isValid(CurveConfigurationForm form, ConstraintValidatorContext context) {
    if (anyNull(form, form.getVersionForm())) {
      return true;
    }
    return isUnique(form.getVersionForm().getValidFrom(), form.getName());
  }

  private boolean isUnique(LocalDate stateDate, String name) {
    if (name == null || stateDate == null) {
      return true;
    }
    return !uniqueSupport.existsByCriteria(
        stateDate, uniqueEntityCriteria(name), CurveConfiguration.class);
  }
}
