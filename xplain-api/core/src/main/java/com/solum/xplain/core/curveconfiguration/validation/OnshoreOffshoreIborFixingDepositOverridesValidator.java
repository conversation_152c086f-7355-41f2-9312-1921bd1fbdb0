package com.solum.xplain.core.curveconfiguration.validation;

import static com.solum.xplain.core.curvegroup.conventions.index.IrIndexCurveConventions.indexCurveNameByIndex;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.error.Error.VALIDATION_ERROR;

import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.index.OffshoreIndices;
import java.util.List;
import java.util.Optional;

/**
 * Validates that if an offshore curve is used for an ibor fixing deposit curve config override,
 * then the same override must apply for the onshore equivalent (and vice-versa).
 */
public class OnshoreOffshoreIborFixingDepositOverridesValidator {

  private OnshoreOffshoreIborFixingDepositOverridesValidator() {}

  public static Optional<ErrorItem> validateIborFixingDepositOverride(
      String curveName, List<String> iborFixingDepositOverridesCurveNames) {
    return ConventionalCurveConfigurations.lookupByName(curveName, CurveType.IR_INDEX)
        .flatMap(
            convention ->
                validateOnshoreOffshore(convention, iborFixingDepositOverridesCurveNames));
  }

  private static Optional<ErrorItem> validateOnshoreOffshore(
      ConventionalCurveConvention convention, List<String> iborFixingDepositOverridesCurveNames) {
    if (convention instanceof IndexCurveConvention indexCurveConvention) {
      if (OffshoreIndices.isOffshore(indexCurveConvention.getIndex())) {
        return checkIfOnshoreExists(indexCurveConvention, iborFixingDepositOverridesCurveNames);
      } else {
        return checkIfOffshoreExists(indexCurveConvention, iborFixingDepositOverridesCurveNames);
      }
    }
    return Optional.empty();
  }

  private static Optional<ErrorItem> checkIfOnshoreExists(
      IndexCurveConvention indexCurveConvention,
      List<String> iborFixingDepositOverridesCurveNames) {
    Optional<String> onshoreEquivalentCurveName =
        indexCurveNameByIndex(OffshoreIndices.convertFromOffshore(indexCurveConvention.getIndex()));
    if (onshoreEquivalentCurveName.isPresent()) {
      if (iborFixingDepositOverridesCurveNames.contains(onshoreEquivalentCurveName.get())) {
        return Optional.empty();
      } else {
        return Optional.of(
            VALIDATION_ERROR.entity(
                "Offshore curve "
                    + indexCurveConvention.getName()
                    + " must have onshore equivalent in overrides for "
                    + CoreInstrumentType.IBOR_FIXING_DEPOSIT.getLabel()));
      }
    }
    return Optional.of(
        PARSING_ERROR.entity(
            "Could not find equivalent onshore curve for " + indexCurveConvention.getName()));
  }

  private static Optional<ErrorItem> checkIfOffshoreExists(
      IndexCurveConvention indexCurveConvention,
      List<String> iborFixingDepositOverridesCurveNames) {
    Optional<String> offshoreEquivalentCurveName =
        indexCurveNameByIndex(OffshoreIndices.convertFromOnshore(indexCurveConvention.getIndex()));
    if (offshoreEquivalentCurveName.isPresent()) {
      if (iborFixingDepositOverridesCurveNames.contains(offshoreEquivalentCurveName.get())) {
        return Optional.empty();
      } else {
        return Optional.of(
            VALIDATION_ERROR.entity(
                "Onshore curve "
                    + indexCurveConvention.getName()
                    + " must have offshore equivalent in overrides for "
                    + CoreInstrumentType.IBOR_FIXING_DEPOSIT.getLabel()));
      }
    }
    return Optional.of(
        PARSING_ERROR.entity(
            "Could not find equivalent offshore curve for " + indexCurveConvention.getName()));
  }
}
