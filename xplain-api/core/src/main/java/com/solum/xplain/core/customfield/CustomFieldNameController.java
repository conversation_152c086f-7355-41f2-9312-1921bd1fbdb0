package com.solum.xplain.core.customfield;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_CUSTOM_FIELD_NAME;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CUSTOM_FIELD_NAME;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.customfield.value.CustomFieldNameCreateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameUpdateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/custom-field-names")
@RequiredArgsConstructor
public class CustomFieldNameController {

  private final CustomFieldNameControllerService service;

  @Operation(summary = "Get all custom field names")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_CUSTOM_FIELD_NAME)
  public List<CustomFieldNameView> list(
      @RequestParam boolean archived, TableFilter tableFilter, Sort sort) {
    return service.list(tableFilter, sort, archived);
  }

  @Operation(summary = "Create custom field name")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_CUSTOM_FIELD_NAME)
  public ResponseEntity<EntityId> create(@Valid @RequestBody CustomFieldNameCreateForm form) {
    return eitherErrorItemResponse(service.insert(form));
  }

  @Operation(summary = "Update custom field name")
  @CommonErrors
  @PutMapping("/{id}")
  @PreAuthorize(AUTHORITY_MODIFY_CUSTOM_FIELD_NAME)
  public ResponseEntity<EntityId> update(
      @PathVariable String id, @Valid @RequestBody CustomFieldNameUpdateForm form) {
    return eitherErrorItemResponse(service.update(id, form));
  }

  @Operation(summary = "Archive custom field name")
  @CommonErrors
  @PutMapping("/{id}/archive")
  @PreAuthorize(AUTHORITY_MODIFY_CUSTOM_FIELD_NAME)
  public ResponseEntity<EntityId> archive(@PathVariable String id) {
    return eitherErrorItemResponse(service.archive(id));
  }
}
