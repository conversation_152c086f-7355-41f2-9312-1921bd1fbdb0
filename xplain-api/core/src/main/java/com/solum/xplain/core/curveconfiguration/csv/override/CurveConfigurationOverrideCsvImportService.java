package com.solum.xplain.core.curveconfiguration.csv.override;

import static com.solum.xplain.core.common.CollectionUtils.join;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.futureVersionExists;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.newVersionViable;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.orErrors;
import static com.solum.xplain.core.error.Error.VALIDATION_ERROR;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.getCardinalityMap;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EitherUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportErrorUtils;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.ProcessedValues;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository;
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.InfoItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.instrument.InstrumentType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveConfigurationOverrideCsvImportService extends LoggingImportService {

  private final CurveConfigurationOverrideCsvLoader loader;
  private final CurveConfigurationRepository repository;

  protected CurveConfigurationOverrideCsvImportService(
      AuditEntryService auditEntryService,
      CurveConfigurationOverrideCsvLoader loader,
      CurveConfigurationRepository repository) {
    super(auditEntryService);
    this.loader = loader;
    this.repository = repository;
  }

  @Override
  protected String getCollection() {
    return CurveConfiguration.CURVE_CONFIGURATION_COLLECTION;
  }

  @Override
  protected String getObjectName() {
    return "Curve configuration overrides";
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importOverrides(
      ImportOptions options, byte[] bytes) {
    var configurations =
        repository.findAllActiveItems(options.getStateDate()).stream()
            .collect(Collectors.toMap(VersionedNamedEntity::getName, Function.identity()));
    return loader
        .parse(bytes, options.parsingMode(), configurations.keySet())
        .map(items -> process(options, items, configurations))
        .fold(
            error -> toErrorReturn(options.getDuplicateAction(), error),
            result -> toReturn(options.getDuplicateAction(), result));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importOverridesForConfig(
      String curveConfigurationId, LocalDate versionDate, ImportOptions options, byte[] bytes) {
    return repository
        .findOneActiveItem(curveConfigurationId, versionDate)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(cc -> processSingle(options, bytes, cc))
        .fold(
            error -> toErrorReturn(options.getDuplicateAction(), error),
            result -> toReturn(options.getDuplicateAction(), result));
  }

  private Either<List<ErrorItem>, ImportResult> processSingle(
      ImportOptions options, byte[] bytes, CurveConfiguration curveConfig) {
    var configurationName = curveConfig.getName();
    return loader
        .parseForConfig(bytes, options.parsingMode(), configurationName)
        .map(result -> process(options, result, Map.of(configurationName, curveConfig)));
  }

  private ImportResult process(
      ImportOptions options,
      CsvParserResult<CurveConfigurationInstrumentCsvOverrides> parserResult,
      Map<String, CurveConfiguration> configurationsMap) {
    var importLogs = importOverrides(options, parserResult.getParsedLines(), configurationsMap);
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private List<LogItem> importOverrides(
      ImportOptions options,
      List<CurveConfigurationInstrumentCsvOverrides> forms,
      Map<String, CurveConfiguration> configurations) {
    return forms.stream()
        .map(f -> getActiveConfiguration(f, configurations, options))
        .collect(collectingAndThen(toList(), EitherUtils::allRightOrFilterLeftList))
        .fold(this::asLogItems, values -> updateCurveConfigurations(values, options));
  }

  private Either<List<ErrorItem>, CurveConfigurationOverrideProcessedValues> getActiveConfiguration(
      CurveConfigurationInstrumentCsvOverrides form,
      Map<String, CurveConfiguration> configurations,
      ImportOptions options) {
    return getActiveConfiguration(form.getConfigurationName(), configurations)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(cc -> processValues(cc, form.getOverrides(), options));
  }

  private List<LogItem> updateCurveConfigurations(
      List<CurveConfigurationOverrideProcessedValues> values, ImportOptions importOptions) {
    return values.stream()
        .map(v -> updateConfiguration(v, importOptions))
        .flatMap(Collection::stream)
        .toList();
  }

  private List<LogItem> updateConfiguration(
      CurveConfigurationOverrideProcessedValues overrides, ImportOptions importOptions) {
    return repository
        .update(
            overrides.getCurveConfiguration().getEntityId(),
            overrides.getCurveConfiguration().getValidFrom(),
            overrides.toUpdateForm(importOptions))
        .fold(l -> asLogItems(List.of(l)), id -> overrides.logItems());
  }

  private Either<ErrorItem, CurveConfiguration> getActiveConfiguration(
      String curveConfigurationName, Map<String, CurveConfiguration> configurationMap) {
    var configuration = configurationMap.get(curveConfigurationName);
    return configuration == null
        ? Either.left(
            VALIDATION_ERROR.entity("Invalid curve configuration name: " + curveConfigurationName))
        : Either.right(configuration);
  }

  private Either<List<ErrorItem>, CurveConfigurationOverrideProcessedValues> processValues(
      CurveConfiguration curveConfiguration,
      List<CurveConfigurationInstrumentCsvOverride> newItems,
      ImportOptions importOptions) {
    var existingItems = existingInstrumentOverrides(curveConfiguration);
    var importItems =
        ImportItems
            .<CurveConfigurationInstrumentCsvOverride, CurveConfigurationInstrumentOverrideKey,
                CurveConfigurationInstrumentCsvOverride>
                builder()
            .existingActiveItems(existingItems)
            .existingItemToKeyFn(CurveConfigurationInstrumentCsvOverride::uniqueKey)
            .importItems(newItems)
            .importItemToKeyFn(CurveConfigurationInstrumentCsvOverride::uniqueKey)
            .build();

    if (importOptions.getDuplicateAction() == DuplicateAction.ERROR) {
      var validationErrors = validate(curveConfiguration, importItems, importOptions);
      if (!validationErrors.isEmpty()) {
        return Either.left(validationErrors);
      }
    }
    var infoItem = infoItem(curveConfiguration, importItems, importOptions);
    var processedValues =
        process(importItems, existingItems, importOptions.getDuplicateAction())
            .thenApply(
                p ->
                    CurveConfigurationOverrideProcessedValues.newOf(
                        curveConfiguration, p.getValues(), infoItem));
    return validateUniqueOverrideFields(processedValues);
  }

  private InfoItem infoItem(
      CurveConfiguration curveConfiguration,
      ImportItems<
              CurveConfigurationInstrumentCsvOverride,
              CurveConfigurationInstrumentOverrideKey,
              CurveConfigurationInstrumentCsvOverride>
          importItems,
      ImportOptions importOptions) {
    var removeSpareItems =
        importOptions.getDuplicateAction() == DuplicateAction.APPEND_DELETE
            || importOptions.getDuplicateAction() == DuplicateAction.REPLACE_DELETE;

    Set<CurveConfigurationInstrumentOverrideKey> newKeys = importItems.getNewKeys();
    Set<CurveConfigurationInstrumentOverrideKey> removeKeys =
        removeSpareItems ? importItems.getSpareKeys() : Set.of();
    var description =
        String.format(
            "Updated %s. Added %d mappings %s and removed %d mapping" + " %s",
            curveConfiguration.getName(),
            newKeys.size(),
            keysString(newKeys),
            removeKeys.size(),
            keysString(removeKeys));
    return InfoItem.of(Error.IMPORT_INFO, entityId(curveConfiguration.getEntityId()), description);
  }

  private String keysString(Set<CurveConfigurationInstrumentOverrideKey> keys) {
    if (keys.isEmpty()) {
      return StringUtils.EMPTY;
    } else {
      return keys.stream().map(this::toKeyString).collect(joining(", ", "[", "]"));
    }
  }

  private Either<List<ErrorItem>, CurveConfigurationOverrideProcessedValues>
      validateUniqueOverrideFields(CurveConfigurationOverrideProcessedValues processedValues) {
    var errors =
        groupOverrideFieldsForInstrument(processedValues.getInstrumentOverrides())
            .entrySet()
            .stream()
            .map(entry -> validateUniqueOverrideInstrument(entry.getKey(), entry.getValue()))
            .flatMap(Collection::stream)
            .toList();

    return Eithers.cond(errors.isEmpty(), errors, processedValues);
  }

  private Map<InstrumentType, List<String>> groupOverrideFieldsForInstrument(
      List<CurveConfigurationInstrumentCsvOverride> overrides) {
    return overrides.stream()
        .filter(m -> !isEmpty(m.getProviderForm().overrideFields()))
        .collect(
            groupingBy(
                CurveConfigurationInstrumentCsvOverride::getInstrumentType,
                Collectors.flatMapping(
                    m -> m.getProviderForm().overrideFields().stream(), toList())));
  }

  private List<ErrorItem> validateUniqueOverrideInstrument(
      InstrumentType instrumentType, List<String> assetNames) {
    var duplicatedCurves =
        getCardinalityMap(assetNames).entrySet().stream()
            .filter(v -> v.getValue() > 1)
            .map(Map.Entry::getKey)
            .toList();
    if (!duplicatedCurves.isEmpty()) {
      return List.of(
          VALIDATION_ERROR.entity(
              String.format(
                  "Overlapping overrides on %s for instrument %s",
                  duplicatedCurves, instrumentType)));
    }
    return List.of();
  }

  private List<CurveConfigurationInstrumentCsvOverride> existingInstrumentOverrides(
      CurveConfiguration curveConfiguration) {
    return Stream.ofNullable(curveConfiguration.getOverrides())
        .flatMap(Collection::stream)
        .flatMap(
            override ->
                override.getInstruments().entrySet().stream()
                    .filter(v -> !v.getValue().isEmpty())
                    .map(
                        i ->
                            CurveConfigurationInstrumentCsvOverride.newOf(
                                override.getPriority(), i.getKey(), i.getValue())))
        .toList();
  }

  private ProcessedValues<CurveConfigurationInstrumentCsvOverride> process(
      ImportItems<
              CurveConfigurationInstrumentCsvOverride,
              CurveConfigurationInstrumentOverrideKey,
              CurveConfigurationInstrumentCsvOverride>
          importItems,
      List<CurveConfigurationInstrumentCsvOverride> existingItems,
      DuplicateAction action) {
    var existing = ProcessedValues.newOf(existingItems);
    return switch (action) {
      case ERROR, APPEND -> existing.process(values -> append(values, importItems));
      case REPLACE_DELETE ->
          existing
              .process(values -> replace(values, importItems))
              .process(values -> append(values, importItems))
              .process(values -> removeMissing(values, importItems));
      case REPLACE ->
          existing
              .process(values -> replace(values, importItems))
              .process(values -> append(values, importItems));
      case APPEND_DELETE ->
          existing
              .process(values -> append(values, importItems))
              .process(values -> removeMissing(values, importItems));
    };
  }

  private ProcessedValues<CurveConfigurationInstrumentCsvOverride> append(
      List<CurveConfigurationInstrumentCsvOverride> existingValues,
      ImportItems<
              CurveConfigurationInstrumentCsvOverride,
              CurveConfigurationInstrumentOverrideKey,
              CurveConfigurationInstrumentCsvOverride>
          importItems) {
    var items =
        ImmutableList.<CurveConfigurationInstrumentCsvOverride>builder().addAll(existingValues);
    importItems.getNewKeys().stream().map(importItems::importItem).forEach(items::add);
    return ProcessedValues.newOf(items.build());
  }

  private ProcessedValues<CurveConfigurationInstrumentCsvOverride> replace(
      List<CurveConfigurationInstrumentCsvOverride> existingValues,
      ImportItems<
              CurveConfigurationInstrumentCsvOverride,
              CurveConfigurationInstrumentOverrideKey,
              CurveConfigurationInstrumentCsvOverride>
          importItems) {
    var items = ImmutableList.<CurveConfigurationInstrumentCsvOverride>builder();
    for (CurveConfigurationInstrumentCsvOverride override : existingValues) {
      var importItem = importItems.importItem(override.uniqueKey());
      items.add(Objects.requireNonNullElse(importItem, override));
    }
    return ProcessedValues.newOf(items.build());
  }

  private ProcessedValues<CurveConfigurationInstrumentCsvOverride> removeMissing(
      List<CurveConfigurationInstrumentCsvOverride> existingValues,
      ImportItems<
              CurveConfigurationInstrumentCsvOverride,
              CurveConfigurationInstrumentOverrideKey,
              CurveConfigurationInstrumentCsvOverride>
          importItems) {
    return existingValues.stream()
        .filter(v -> !importItems.getSpareKeys().contains(v.uniqueKey()))
        .collect(collectingAndThen(toList(), ProcessedValues::newOf));
  }

  private String toKeyString(CurveConfigurationInstrumentOverrideKey key) {
    return String.format("Order: %s instrument: %s", key.getPriority(), key.getInstrumentType());
  }

  private List<ErrorItem> validate(
      CurveConfiguration curveConfig,
      ImportItems<
              CurveConfigurationInstrumentCsvOverride,
              CurveConfigurationInstrumentOverrideKey,
              CurveConfigurationInstrumentCsvOverride>
          importItems,
      ImportOptions options) {
    var duplicateErrors =
        errorsList(importItems.getDuplicateKeys(), ImportErrorUtils::duplicateItem);
    var spareErrors = errorsList(importItems.getSpareKeys(), ImportErrorUtils::missingItem);
    var futureVersionsErrors = validateFutureVersions(curveConfig, options.getStateDate());
    var viableNewVersionsErrors = validateViableNewVersion(curveConfig, options.getStateDate());
    return join(duplicateErrors, spareErrors, futureVersionsErrors, viableNewVersionsErrors);
  }

  private List<ErrorItem> errorsList(
      Set<CurveConfigurationInstrumentOverrideKey> keys, Function<String, ErrorItem> function) {
    return keys.stream().map(k -> function.apply(toKeyString(k))).toList();
  }

  private List<ErrorItem> validateViableNewVersion(
      CurveConfiguration curveConfiguration, LocalDate stateDate) {
    return orErrors(
        stateDate.isAfter(curveConfiguration.getValidFrom()),
        newVersionViable(curveConfiguration.getName()));
  }

  private List<ErrorItem> validateFutureVersions(
      CurveConfiguration curveConfiguration, LocalDate stateDate) {
    boolean hasFutureVersions = hasFutureVersions(curveConfiguration.getName(), stateDate);
    return orErrors(hasFutureVersions, futureVersionExists(curveConfiguration.getName()));
  }

  private boolean hasFutureVersions(String configurationName, LocalDate stateDate) {
    var searchForm = new CurveConfigurationSearchForm(configurationName, stateDate);
    return repository.futureVersions(searchForm).notEmpty();
  }
}
