package com.solum.xplain.core.externalsource;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.externalsource.value.IdentifierSourceCreateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceUpdateForm;
import com.solum.xplain.core.externalsource.value.IdentifierSourceView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class IdentifierSourceControllerService {

  private final IdentifierSourceRepository repository;

  public List<IdentifierSourceView> list(TableFilter tableFilter, Sort sort, boolean archived) {
    return repository.list(tableFilter, sort, archived);
  }

  public Either<ErrorItem, EntityId> insert(IdentifierSourceCreateForm form) {
    return repository.insert(form);
  }

  public Either<ErrorItem, EntityId> update(String id, IdentifierSourceUpdateForm form) {
    return repository.update(id, form);
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return repository.archive(id);
  }
}
