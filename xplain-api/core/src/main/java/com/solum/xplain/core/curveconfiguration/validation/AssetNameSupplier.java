package com.solum.xplain.core.curveconfiguration.validation;

import static com.solum.xplain.core.curvegroup.conventions.CurveConfigurations.ALL_CURVES;
import static com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceConfigurations.VOLATILITY_SURFACES;

import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceConfiguration;
import java.util.Collection;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class AssetNameSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Stream.concat(
            ALL_CURVES.stream().map(CurveConvention::getName),
            VOLATILITY_SURFACES.stream().map(VolatilitySurfaceConfiguration::getName))
        .toList();
  }
}
