package com.solum.xplain.core.curveconfiguration.csv.override;

import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.CONFIGURATION_NAME_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.INSTRUMENT_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.OVERRIDE_CURVES_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.OVERRIDE_FX_PAIR_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.OVERRIDE_SECTORS_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.PRIMARY_PROVIDER_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.SECONDARY_PROVIDER_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.override.CurveConfigurationOverrideCsvLoader.OVERRIDE_PRIORITY_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideView;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurveConfigurationOverrideCsvMapper {

  public static List<CsvRow> toCsvRows(CurveConfigurationView configuration) {
    return Stream.ofNullable(configuration.getOverrides())
        .flatMap(Collection::stream)
        .flatMap(
            o ->
                o.getInstruments().entrySet().stream()
                    .filter(i -> i.getValue().getPrimary() != null)
                    .sorted(Comparator.comparing(v -> v.getKey().name()))
                    .map(
                        i ->
                            toCsvRow(
                                configuration.getName(),
                                o.getPriority(),
                                i.getKey().getLabel(),
                                i.getValue())))
        .toList();
  }

  private static CsvRow toCsvRow(
      String nameField,
      Integer priorityField,
      String instrumentField,
      CurveConfigurationProviderOverrideView override) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(CONFIGURATION_NAME_FIELD, nameField));
    builder.add(new CsvField(OVERRIDE_PRIORITY_FIELD, priorityField));
    builder.add(new CsvField(INSTRUMENT_FIELD, instrumentField));
    builder.add(new CsvField(PRIMARY_PROVIDER_FIELD, override.getPrimary()));
    builder.add(new CsvField(SECONDARY_PROVIDER_FIELD, override.getSecondary()));
    if (override.getAssetNames() != null) {
      var curvesField = String.join("|", override.getAssetNames());
      builder.add(new CsvField(OVERRIDE_CURVES_FIELD, curvesField));
    }
    if (override.getFxPairs() != null) {
      var fxPairsField = String.join("|", override.getFxPairs());
      builder.add(new CsvField(OVERRIDE_FX_PAIR_FIELD, fxPairsField));
    }
    if (override.getSectors() != null) {
      var sectorsField = String.join("|", override.getSectors());
      builder.add(new CsvField(OVERRIDE_SECTORS_FIELD, sectorsField));
    }
    return new CsvRow(builder.build());
  }
}
