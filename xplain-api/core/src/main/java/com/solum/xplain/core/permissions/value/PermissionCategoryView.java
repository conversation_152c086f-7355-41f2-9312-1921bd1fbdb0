package com.solum.xplain.core.permissions.value;

import com.solum.xplain.core.permissions.extension.PermissionCategory;
import com.solum.xplain.core.permissions.extension.PermissionCategoryGroup;
import java.util.List;
import lombok.Data;

@Data
public class PermissionCategoryView {
  private final PermissionCategoryGroup permissionGroup;
  private final List<PermissionCategory> permissionCategories;

  public static PermissionCategoryView newOf(
      PermissionCategoryGroup group, List<PermissionCategory> categories) {
    return new PermissionCategoryView(group, categories);
  }
}
