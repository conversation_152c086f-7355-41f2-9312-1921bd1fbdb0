package com.solum.xplain.core.curveconfiguration.csv.override;

import com.solum.xplain.core.instrument.InstrumentType;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ConfigurationOverrideInstrumentCsvForm {

  private final String configurationName;
  private final ConfigurationOverrideInstrumentProviderCsvForm instrumentForm;

  public static ConfigurationOverrideInstrumentCsvForm newOf(
      String configurationName,
      InstrumentType type,
      String primaryProvider,
      String secondaryProvider,
      List<String> assetNames,
      List<String> sectors,
      List<String> fxPairs) {
    var instrumentForm =
        ConfigurationOverrideInstrumentProviderCsvForm.newOf(
            type, assetNames, sectors, fxPairs, primaryProvider, secondaryProvider);
    return new ConfigurationOverrideInstrumentCsvForm(configurationName, instrumentForm);
  }
}
