package com.solum.xplain.valuation.calculation.breakeven;

import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.product.ProductTrade;
import com.opengamma.strata.product.capfloor.IborCapFloorTrade;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.PaymentSchedule;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.SwapLegType;
import com.opengamma.strata.product.swap.SwapTrade;
import com.solum.xplain.extensions.index.OvernightTermRateCalculation;
import com.solum.xplain.valuation.calculation.capfloor.IborCapFloorTradeConverter;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SwapParRateCalculationsFactory {

  private static final String INVALID_TRADE_CLASS_ERROR =
      "SwapParRateCalculation is not supported for trade %s";

  public static SwapParRateCalculation parRateCalculation(ProductTrade trade) {
    var swapTrade = getTrade(trade);
    if (isFixed(swapTrade)) {
      return fixedParRateCalculation(swapTrade);
    } else {
      return new SpreadLegSwapParRateCalculation(swapTrade);
    }
  }

  private static SwapParRateCalculation fixedParRateCalculation(SwapTrade swapTrade) {
    boolean sameAccrualPaymentFreq = sameAccrualFreqPaymentFreq(swapTrade);
    boolean hasCompounding = !hasCompoundingNone(swapTrade) || !sameAccrualPaymentFreq;
    boolean hasOneOneFixedDayCount = hasOneOneFixedDayCount(swapTrade);

    if (!hasOneOneFixedDayCount) {
      if (hasCompounding) {
        return new IterativeFixedLegParRateCalculation(swapTrade);
      }

      if (isInflation(swapTrade)) {
        return new FixedInflationParRateCalculation(swapTrade);
      }
    }

    return new FixedLegSwapParRateCalculation(swapTrade);
  }

  private static SwapTrade getTrade(ProductTrade trade) {
    if (trade instanceof IborCapFloorTrade) {
      return IborCapFloorTradeConverter.of((IborCapFloorTrade) trade).toSwapTrade();
    } else if (trade instanceof SwapTrade) {
      return (SwapTrade) trade;
    } else {
      throw new IllegalArgumentException(
          String.format(INVALID_TRADE_CLASS_ERROR, trade.getClass().getSimpleName()));
    }
  }

  private static boolean isFixed(SwapTrade trade) {
    return trade.getProduct().getLegs().stream().anyMatch(a -> a.getType().isFixed());
  }

  private static boolean isInflation(SwapTrade trade) {
    return !trade.getProduct().getLegs(SwapLegType.INFLATION).isEmpty();
  }

  private static boolean hasOneOneFixedDayCount(SwapTrade swapTrade) {
    return swapTrade.getProduct().getLegs(SwapLegType.FIXED).stream()
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast)
        .map(RateCalculationSwapLeg::getCalculation)
        .map(RateCalculation::getDayCount)
        .allMatch(DayCounts.ONE_ONE::equals);
  }

  private static boolean hasCompoundingNone(SwapTrade swapTrade) {
    return swapTrade.getProduct().getLegs(SwapLegType.FIXED).stream()
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast)
        .map(RateCalculationSwapLeg::getPaymentSchedule)
        .map(PaymentSchedule::getCompoundingMethod)
        .allMatch(CompoundingMethod.NONE::equals);
  }

  private static boolean sameAccrualFreqPaymentFreq(SwapTrade swapTrade) {
    return swapTrade.getProduct().getLegs(SwapLegType.FIXED).stream()
        .filter(RateCalculationSwapLeg.class::isInstance)
        .map(RateCalculationSwapLeg.class::cast)
        .findFirst()
        .map(
            l -> {
              var accrualFrequency = l.getAccrualSchedule().getFrequency();
              var paymentFrequency = l.getPaymentSchedule().getPaymentFrequency();
              return accrualFrequency.equals(paymentFrequency);
            })
        .orElse(true);
  }
}
