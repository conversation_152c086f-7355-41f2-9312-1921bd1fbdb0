package com.solum.xplain.core.authentication.converter;

import com.google.common.annotations.VisibleForTesting;
import com.solum.xplain.core.authentication.CacheableAuthenticationProxy;
import com.solum.xplain.core.authentication.XplainPrincipalResolver;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.config.properties.OAuth2CustomProperties;
import java.util.Objects;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthentication;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

@Component
@ConditionalOnProperty(name = "spring.security.oauth2.resourceserver.jwt.issuer-uri")
@Profile("!mockMvc")
public class JwtToPrincipalConverter implements Converter<Jwt, AbstractAuthenticationToken> {

  private final XplainPrincipalResolver xplainPrincipalResolver;
  private final CacheableAuthenticationProxy cacheableAuthenticationProxy;
  private final OAuth2CustomProperties oAuth2CustomProperties;

  public JwtToPrincipalConverter(
      XplainPrincipalResolver xplainPrincipalResolver,
      CacheableAuthenticationProxy cacheableAuthenticationProxy,
      OAuth2CustomProperties oAuth2CustomProperties) {
    this.xplainPrincipalResolver = xplainPrincipalResolver;
    this.cacheableAuthenticationProxy = cacheableAuthenticationProxy;
    this.oAuth2CustomProperties = oAuth2CustomProperties;
    Assert.notNull(oAuth2CustomProperties.getClaims(), "OAuth2 JWT claims not configured");
  }

  @Override
  public AbstractAuthenticationToken convert(Jwt jwt) {
    var accessToken =
        new OAuth2AccessToken(
            OAuth2AccessToken.TokenType.BEARER,
            jwt.getTokenValue(),
            jwt.getIssuedAt(),
            jwt.getExpiresAt());
    var principal =
        cacheableAuthenticationProxy.fetchCachedOrNewPrincipal(jwt, this::resolvePrincipal);
    return new BearerTokenAuthentication(principal, accessToken, principal.getAuthorities());
  }

  @VisibleForTesting
  XplainPrincipal resolvePrincipal(Jwt jwt) {
    return xplainPrincipalResolver.resolvePrincipal(
        jwt.getSubject(),
        jwt.getClaim(oAuth2CustomProperties.getClaims().getUsernameClaim()),
        jwt.getClaim(oAuth2CustomProperties.getClaims().getClientIdClaim()),
        jwt.getClaim(oAuth2CustomProperties.getClaims().getRolesClaim()),
        jwt.getClaim(oAuth2CustomProperties.getClaims().getTeamsClaim()),
        new JwtGrantedAuthoritiesConverter().convert(jwt),
        jwt.getClaims(),
        Objects.equals(
            jwt.getClaim(oAuth2CustomProperties.getClaims().getPrincipalTypeClaim()),
            oAuth2CustomProperties.getClaims().getPrincipalTypeClientValue()));
  }
}
