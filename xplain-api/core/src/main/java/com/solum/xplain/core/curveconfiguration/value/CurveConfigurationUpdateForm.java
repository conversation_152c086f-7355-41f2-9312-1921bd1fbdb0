package com.solum.xplain.core.curveconfiguration.value;

import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curveconfiguration.validation.ValidCurveConfigInstrumentSize;
import com.solum.xplain.core.curveconfiguration.validation.ValidCurveConfigurationOverrides;
import com.solum.xplain.core.curvegroup.curvegroup.validation.ValidCurveGroupId;
import com.solum.xplain.core.instrument.InstrumentType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class CurveConfigurationUpdateForm implements HasVersionForm {

  @NotEmpty @ValidCurveGroupId private String curveGroupId;

  @Valid @NotEmpty @ValidCurveConfigInstrumentSize
  private Map<InstrumentType, MarketDataProviderForm> instruments;

  @Valid @ValidCurveConfigurationOverrides private List<CurveConfigurationOverrideForm> overrides;

  @Valid @NotNull private NewVersionFormV2 versionForm;
}
