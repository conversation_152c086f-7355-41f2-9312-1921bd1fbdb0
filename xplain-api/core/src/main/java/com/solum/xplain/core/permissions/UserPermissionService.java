package com.solum.xplain.core.permissions;

import com.solum.xplain.core.permissions.extension.XplainPermission;
import com.solum.xplain.core.roles.Role;
import com.solum.xplain.core.roles.RoleRepository;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class UserPermissionService {
  private final PermissionResolver permissionResolver;
  private final RoleRepository roleRepository;

  public UserPermissionService(
      PermissionResolver permissionResolver, RoleRepository roleRepository) {
    this.permissionResolver = permissionResolver;
    this.roleRepository = roleRepository;
  }

  public List<XplainPermission> fromExternalRoles(List<String> externalIds) {
    return roleRepository.activeRolesByExternalIds(externalIds).stream()
        .map(Role::getRolePermissions)
        .flatMap(Collection::stream)
        .distinct()
        .map(
            p ->
                permissionResolver.getBySubCategoryAndUserType(p.getSubCategory(), p.getUserType()))
        .flatMap(Collection::stream)
        .distinct()
        .toList();
  }
}
