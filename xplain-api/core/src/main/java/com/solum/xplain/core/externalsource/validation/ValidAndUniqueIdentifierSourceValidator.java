package com.solum.xplain.core.externalsource.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.externalsource.IdentifierSourceRepository;
import com.solum.xplain.core.externalsource.value.IdentifierSourceCreateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ValidAndUniqueIdentifierSourceValidator
    implements ConstraintValidator<ValidAndUniqueIdentifierSource, IdentifierSourceCreateForm> {

  private static final String RESTRICTED_ID = "XPLAIN";
  private final IdentifierSourceRepository repository;

  @Override
  public boolean isValid(IdentifierSourceCreateForm form, ConstraintValidatorContext context) {
    if (isNotEmpty(form.getExternalSourceId())) {
      context.disableDefaultConstraintViolation();
      if (RESTRICTED_ID.equals(form.getExternalSourceId())) {
        context
            .buildConstraintViolationWithTemplate("{ValidIdentifierSourceExtIdRestricted}")
            .addPropertyNode(IdentifierSourceCreateForm.Fields.externalSourceId)
            .addConstraintViolation();
        return false;
      } else if (repository.existsByExternalId(form.getExternalSourceId())) {
        context
            .buildConstraintViolationWithTemplate("{ValidIdentifierSourceExtIdUnique}")
            .addPropertyNode(IdentifierSourceCreateForm.Fields.externalSourceId)
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
