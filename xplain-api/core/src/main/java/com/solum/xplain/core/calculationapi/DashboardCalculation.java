package com.solum.xplain.core.calculationapi;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;

public interface DashboardCalculation {

  default Either<ErrorItem, EntityId> calculate(
      String dashboardId,
      LocalDate valuationDate,
      AuditUser createdBy,
      PortfolioSettings<CompanyLegalEntitySettingsView> settings,
      MarketDataSourceType sourceType,
      BitemporalDate stateDate,
      List<ProductType> productTypes) {
    return calculate(
        dashboardId,
        valuationDate,
        createdBy,
        settings.getSettings().getValuationSettings(),
        settings.getView(),
        sourceType,
        stateDate,
        productTypes);
  }

  Either<ErrorItem, EntityId> calculate(
      String dashboardId,
      LocalDate valuationDate,
      AuditUser createdBy,
      CompanyLegalEntityValuationSettingsView settings,
      PortfolioCondensedView portfolio,
      MarketDataSourceType sourceType,
      BitemporalDate stateDate,
      List<ProductType> productTypes);

  List<ProductType> productTypesForDashboardCalculation(
      String portfolioId, BitemporalDate stateDate, List<ProductType> productTypes);
}
