package com.solum.xplain.core.customfield;

import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.Archivable;
import com.solum.xplain.core.common.diff.Diffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Data
@EqualsAndHashCode(exclude = {"createdAt", "lastModifiedAt"})
@FieldNameConstants
public class CustomFieldName implements Archivable<CustomFieldName>, Diffable<CustomFieldName> {

  @Id private String id;
  private String name;
  private String externalId;
  private boolean archived;
  @CreatedBy private AuditUser createdBy;
  @LastModifiedBy private AuditUser lastModifiedBy;
  @CreatedDate private LocalDateTime createdAt;
  @LastModifiedDate private LocalDateTime lastModifiedAt;
  private List<AuditLog> auditLogs;

  public void addAuditLog(AuditLog log) {
    if (auditLogs == null) {
      auditLogs = new ArrayList<>();
    }
    auditLogs.add(log);
  }

  public CustomFieldName archived() {
    this.archived = true;
    return this;
  }

  @Override
  public VersionDiffs diff(CustomFieldName obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append(CustomFieldName.Fields.externalId, this.externalId, obj.externalId)
            .append(CustomFieldName.Fields.name, this.name, obj.name)
            .build());
  }
}
